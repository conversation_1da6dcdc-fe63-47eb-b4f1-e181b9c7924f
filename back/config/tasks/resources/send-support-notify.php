<?php

declare(strict_types=1);

use app\back\entities\YhOperator;
use app\back\entities\Site;
use app\back\entities\User;

return [
    'send-notify-big-dep-users-vv' => ['class' => \app\back\modules\task\actions\send\NotifyBigDepUsersTask::class, 'siteIds' => [Site::VV, Site::VBET, Site::ICG, Site::GGB, Site::VERDE, Site::HIT, Site::NVC, Site::VSP], 'localeFilter' => ['!=', 'ru'], 'currency' => 'EUR', 'depThreshold' => 500, 'contactName' => 'big_dep_users_vv'],
    'send-notify-big-win-back' => ['class' => \app\back\modules\task\actions\send\NotifyBigWinBackTask::class, 'pmRecipientsGroup' => [YhOperator::GROUP_VIP_INBOUND, YhOperator::GROUP_VIP_TL, YhOperator::GROUP_VIP_OUTBOUND, YhOperator::GROUP_VIP_ULTRA], 'depSumThresholdRub' => 30000],
    'send-notify-cg-big-deps' => \app\back\modules\task\actions\send\NotifyCgBigDepsTask::class,
    'send-notify-cid-vip-awol-dep-to-parent-pm-cherry' => ['class' => \app\back\modules\task\actions\send\NotifyCidVipAwolDepToParentPmTask::class, 'parentPersonalManagerEmail' => '<EMAIL>', 'addRecipient' => ['<EMAIL>' => ['<EMAIL>']]],
    'send-notify-new-phone' => ['class' => \app\back\modules\task\actions\send\NotifyNewPhoneTask::class, 'parentPersonalManagerEmail' => '<EMAIL>'],
    'send-notify-new-big-dep-users' => ['class' => \app\back\modules\task\actions\send\NotifyNewBigDepUsersTask::class, 'siteIds' => array_merge(Site::getVipSites(), [Site::GGB]), 'contactName' => 'new_big_dep_users_report'],
    'send-notify-expiring-pgp' => ['class' => \app\back\modules\task\actions\send\NotifyExpiringPgpTask::class],
    'send-notify-ultra-first-dep' => ['class' => \app\back\modules\task\actions\send\NotifyUltraFirstDepTask::class, 'depInterval' => '20 days'],
    'send-notify-ultra-first-login' => ['class' => \app\back\modules\task\actions\send\NotifyUltraFirstLoginTask::class, 'loginInterval' => '20 days'],
    'send-notify-ultra-no-contact' => ['class' => \app\back\modules\task\actions\send\NotifyUltraNoContactTask::class, 'inactiveInterval' => '14 days'],
    'send-notify-ultra-no-dep' => ['class' => \app\back\modules\task\actions\send\NotifyUltraNoDepTask::class, 'inactiveInterval' => '7 days', 'statuses' => [User::STATUS_ULTRA], 'subjectPrefix' => 'Ultra'],
    'send-notify-vip-no-dep' => ['class' => \app\back\modules\task\actions\send\NotifyUltraNoDepTask::class, 'inactiveInterval' => '7 days', 'statuses' => [User::STATUS_VIP], 'siteIds' => [Site::GGB, Site::GGUA], 'subjectPrefix' => 'VIP'],
    'send-notify-asp-no-dep' => ['class' => \app\back\modules\task\actions\send\NotifyUltraNoDepTask::class, 'inactiveInterval' => '7 days', 'statuses' => [User::STATUS_ASP], 'siteIds' => [Site::GGB, Site::GGUA], 'subjectPrefix' => 'ASP'],
    'send-notify-users-inactive' => ['class' => \app\back\modules\task\actions\send\NotifyUsersInactiveTask::class, 'operatorsGroup' => YhOperator::GROUP_VIP_INBOUND, 'daysInterval' => 4],
    'send-notify-vip-awol-dep-info' => \app\back\modules\task\actions\send\NotifyVipAwolDepInfoTask::class,
    'send-notify-vip-risk-dep-info' => ['class' => \app\back\modules\task\actions\send\NotifyVipRiskDepInfoTask::class, 'sites' => [['site_id' => [...Site::PLATFORM_SITES_SMEN, Site::VV]], ['site_id' => [Site::ICG], 'locale' => ['=', 'ru_RU']]]],
    'send-notify-vip-awol-login' => ['class' => \app\back\modules\task\actions\send\NotifyVipAwolLoginTask::class, 'depThreshold' => 30000, 'parentPersonalManagerEmail' => '<EMAIL>'],
    'send-notify-vip-big-dep' => ['class' => \app\back\modules\task\actions\send\NotifyVipBigDepTask::class, 'pmRecipientsGroup' => [YhOperator::GROUP_VIP_INBOUND, YhOperator::GROUP_VIP_TL, YhOperator::GROUP_VIP_OUTBOUND, YhOperator::GROUP_VIP_OUTBOUND_CUT,  YhOperator::GROUP_VIP_INBOUND_CUT], 'leadCopy' => true],
    'send-notify-vip-dep-fail' => ['class' => \app\back\modules\task\actions\send\NotifyVipDepFailTask::class, 'pmRecipientsGroup' => [YhOperator::GROUP_VIP_INBOUND, YhOperator::GROUP_VIP_TL, YhOperator::GROUP_VIP_OUTBOUND]],
    'send-notify-vip-dep-info' => ['class' => \app\back\modules\task\actions\send\NotifyVipDepInfoTask::class, 'pmRecipientsGroup' => [YhOperator::GROUP_VIP_INBOUND, YhOperator::GROUP_VIP_TL, YhOperator::GROUP_VIP_OUTBOUND, YhOperator::GROUP_VIP_OUTBOUND_CUT,  YhOperator::GROUP_VIP_INBOUND_CUT], 'leadCopy' => true, 'showExtraColumns' => true],
    'send-notify-jelly-dep-info' => ['class' => \app\back\modules\task\actions\send\NotifyJellyDepInfoTask::class, 'pmRecipientsGroup' => [YhOperator::GROUP_VIP_INBOUND, YhOperator::GROUP_VIP_TL, YhOperator::GROUP_VIP_OUTBOUND]],
    'send-notify-vip-new-dep-info' => \app\back\modules\task\actions\send\NotifyVipNewDepInfoTask::class,
    'send-notify-vip-wd-fail' => ['class' => \app\back\modules\task\actions\send\NotifyVipWdFailTask::class, 'excludeSiteIds' => [Site::GGB, Site::GGUA]],
    'send-notify-vip-inactive-deposit' => ['class' => \app\back\modules\task\actions\send\NotifyVipInactiveDepositTask::class, 'contactName' => 'vip_inactive_deposit', 'exclude' => [['site_id' => [Site::GGB]], ['site_id' => [Site::VV, Site::ICG], 'locale' => ['!=', 'ru']]]],
    'send-notify-vip-wd-process' => ['class' => \app\back\modules\task\actions\send\NotifyVipWdProcessTask::class, 'pmRecipientsGroup' => [YhOperator::GROUP_VIP_OUTBOUND], 'addRecipient' => ['@all' => ['<EMAIL>']]],
    'send-notify-wd-new' => ['class' => \app\back\modules\task\actions\send\NotifyWdNewTask::class, 'pmRecipientsGroup' => [YhOperator::GROUP_VIP_INBOUND, YhOperator::GROUP_VIP_TL, YhOperator::GROUP_VIP_OUTBOUND]],
    'send-notify-week-big-dep-users' => ['class' => \app\back\modules\task\actions\send\NotifyWeekBigDepUsersTask::class, 'siteIds' => array_merge(Site::getVipSites(), [Site::GGB]), 'contactName' => 'week_big_dep_users_report'],
    'send-notify-weekend-big-deps' => ['class' => \app\back\modules\task\actions\send\NotifyWeekendBigDepsTask::class, 'parentPersonalManagerEmail' => '<EMAIL>'],
    'send-notify-vip-wd-big-sum' => ['class' => \app\back\modules\task\actions\send\NotifyVipWdBigSumTask::class, 'siteIds' => [Site::GGB]],
];
