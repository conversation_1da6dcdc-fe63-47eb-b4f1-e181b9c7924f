<?php

declare(strict_types=1);

use app\back\config\tasks\Res;
use app\back\entities\enums\WpAffOwner;
use app\back\entities\Rate;
use app\back\entities\Site;
use app\back\entities\User;
use app\back\modules\reports\columns\Operators as FilterOperators;
use app\back\modules\task\actions\send\{
    ConversionByCountriesTask,
    KpiTask};

return [
    Res::SEND => [
        ... require __DIR__ . '/send-support.php',
        ... require __DIR__ . '/send-support-notify.php',
        ... require __DIR__ . '/send-crm.php',
        'send-active-users' => ['class' => \app\back\modules\task\actions\send\ActiveUsersTask::class, 'siteIds' => [...Site::getVipSites(), Site::VERDE, Site::VBET, Site::VSP, Site::NVC, Site::HIT], 'contactName' => 'active_users'],
        'send-affiliate-media-buy' => ['class' => \app\back\modules\task\actions\send\AffiliateMediaBuyTask::class, 'contactName' => 'affiliate_media_buy_report_emails', 'siteGroups' => ['group' => [Site::GROUP_SMEN, Site::GROUP_GPD, Site::GROUP_GI, Site::GROUP_GGB]]],
        'send-cohort-daily-bet-ggb' => \app\back\modules\task\actions\send\CohortDailyBetGgbTask::class,
        'send-cohort-daily-dep-ggb' => \app\back\modules\task\actions\send\CohortDailyDepGgbTask::class,
        'send-cohort-daily-retention-ggb' => \app\back\modules\task\actions\send\CohortDailyRetentionGgbTask::class,
        'send-confirm-rate' => \app\back\modules\task\actions\send\ConfirmRateTask::class,
        'send-conversion-by-countries-vv' => ['class' => ConversionByCountriesTask::class, 'siteIds' => [Site::VV], 'contactName' => 'conversion_by_countries_vv_report_emails'],
        'send-conversion-by-countries-vbet' => ['class' => ConversionByCountriesTask::class, 'siteIds' => [Site::VBET], 'contactName' => 'conversion_by_countries_vbet_report_emails'],
        'send-conversion-by-countries-ggb' => ['class' => ConversionByCountriesTask::class, 'siteIds' => [Site::GGB], 'contactName' => 'conversion_by_countries_ggb_report_emails'],
        'send-conversion-by-countries-vsp' => ['class' => ConversionByCountriesTask::class, 'siteIds' => [Site::VSP], 'contactName' => 'conversion_by_countries_vsp_report_emails'],
        'send-conversion-fail-summary-ggb' => ['class' => \app\back\modules\task\actions\send\ConversionFailSummaryTask::class, 'siteIds' => [Site::GGUA, Site::GGB, Site::CSBET], 'contactName' => 'conversion_fail_summary_ggb_report_emails'],
        'send-conversion-by-countries-mrb' => ['class' => ConversionByCountriesTask::class, 'siteIds' => [Site::MRB], 'contactName' => 'conversion_by_countries_mrb_report_emails'],
        'send-conversion-by-countries-icg' => ['class' => ConversionByCountriesTask::class, 'siteIds' => [Site::ICG], 'contactName' => 'conversion_by_countries_icg_report_emails'],
        'send-conversion-by-countries-s7' => ['class' => ConversionByCountriesTask::class, 'siteIds' => [Site::S7], 'contactName' => 'conversion_by_countries_s7_report_emails'],
        'send-conversion-by-countries-win' => ['class' => ConversionByCountriesTask::class, 'siteIds' => [Site::WIN], 'contactName' => 'conversion_by_countries_win_report_emails'],
        'send-conversion-by-countries-los' => ['class' => ConversionByCountriesTask::class, 'siteIds' => [Site::LOS], 'contactName' => 'conversion_by_countries_los_report_emails'],
        'send-conversion-by-countries-vox' => ['class' => ConversionByCountriesTask::class, 'siteIds' => [Site::VOX], 'contactName' => 'conversion_by_countries_vox_report_emails'],
        'send-conversion-by-countries-verde' => ['class' => ConversionByCountriesTask::class, 'siteIds' => [Site::VERDE], 'contactName' => 'conversion_by_countries_verde_report_emails'],
        'send-conversion-by-countries-nvc' => ['class' => ConversionByCountriesTask::class, 'siteIds' => [Site::NVC], 'contactName' => 'conversion_by_countries_nvc_report_emails'],
        'send-conversion-by-countries-hit' => ['class' => ConversionByCountriesTask::class, 'siteIds' => [Site::HIT], 'contactName' => 'conversion_by_countries_hit_report_emails'],
        'send-conversion-by-countries-vv-wm-65156' => ['class' => ConversionByCountriesTask::class, 'siteIds' => [Site::VV], 'webmasterIds' => [49137, 65156, 56640, 118252], 'contactName' => 'conversion_by_countries_vv_wm_65156_emails'],
        'send-conversion-by-countries-vv-wm-66340' => ['class' => ConversionByCountriesTask::class, 'siteIds' => [Site::VV], 'webmasterIds' => [66340, 47706, 64953], 'contactName' => 'conversion_by_countries_vv_wm_66340_emails'],
        'send-conversion-by-countries-vv-wm-103407' => ['class' => ConversionByCountriesTask::class, 'siteIds' => [Site::VV], 'webmasterIds' => [103407, 87607, 87016, 112206], 'contactName' => 'conversion_by_countries_vv_wm_103407_emails'],
        'send-conversion-by-countries-sltr' => ['class' => ConversionByCountriesTask::class, 'siteIds' => [Site::SLTR], 'contactName' => 'conversion_by_countries_sltr_report_emails'],
        'send-conversion-by-countries-awi' => ['class' => ConversionByCountriesTask::class, 'siteIds' => [Site::AWI], 'contactName' => 'conversion_by_countries_awi_report_emails'],
        'send-conversion-by-countries-fc' => ['class' => ConversionByCountriesTask::class, 'siteIds' => [Site::FC], 'contactName' => 'conversion_by_countries_fc_report_emails'],
        'send-conversion-by-affiliates-gg-casino-betting' => ['class' => \app\back\modules\task\actions\send\ConversionByAffiliatesTask::class, 'title' => 'GG:Casino-Betting affiliates', 'siteGroups' => [Site::GROUP_GGB], 'affOwners' => [WpAffOwner::GG_CASINO_BETTING], 'contactName' => 'conversion_by_affiliates_gg_casino_betting'],
        'send-daily-games' => ['class' => \app\back\modules\task\actions\send\DailyGamesTask::class, 'siteGroups' => [Site::GROUP_SMEN, Site::GROUP_GPD, Site::GROUP_GI, Site::GROUP_GGB], 'contactName' => 'daily_games_report'],
        'send-income-summary' => \app\back\modules\task\actions\send\IncomeSummaryTask::class,
        'send-income-summary-evening' => \app\back\modules\task\actions\send\IncomeSummaryEveningTask::class,
        'send-income-summary-locations' => \app\back\modules\task\actions\send\IncomeSummaryByLocationsTask::class,
        'send-incorrect-affiliates-refcodes' => ['class' => \app\back\modules\task\actions\send\IncorrectAffiliatesRefcodesTask::class, 'siteGroups' => [Site::GROUP_SMEN, Site::GROUP_GPD, Site::GROUP_GI, Site::GROUP_GGB], 'contactName' => 'incorrect_affiliates_refcodes'],
        'send-kpi-ru' => ['class' => KpiTask::class, 'subject' => 'Daily KPI RU', 'contactName' => 'kpi_ru_report_emails', 'currency' => Rate::RUB, 'siteGroups' => [Site::GROUP_SMEN, Site::GROUP_GPD, Site::GROUP_GI, Site::GROUP_GGB], 'pigCountry' => true],
        'send-kpi-int' => ['class' => KpiTask::class, 'subject' => 'Daily KPI Int', 'contactName' => 'kpi_int_report_emails', 'currency' => Rate::EUR, 'siteGroups' => [Site::GROUP_SMEN, Site::GROUP_GPD, Site::GROUP_GI, Site::GROUP_GGB], 'pigCountry' => false],
        'send-kpi-full-eur' => ['class' => KpiTask::class, 'subject' => 'Daily KPI Full', 'contactName' => 'kpi_full_eur_report_emails', 'currency' => Rate::EUR, 'siteGroups' => [Site::GROUP_SMEN, Site::GROUP_GPD, Site::GROUP_GI, Site::GROUP_GGB]],
        'send-kpi-ggbet-full' => ['class' => KpiTask::class, 'subject' => 'Daily KPI GGBet Full', 'contactName' => 'kpi_ggbet_full_report_emails', 'currency' => Rate::EUR, 'siteIds' => [Site::GGB]],
        'send-kpi-vbet' => ['class' => \app\back\modules\task\actions\send\KpiVbetTask::class, 'subject' => 'Daily KPI VBET', 'contactName' => 'kpi_vbet_report_emails', 'currency' => Rate::EUR, 'siteIds' => [Site::VBET]],
        'send-kpi-ys-int' => ['class' => KpiTask::class, 'subject' => 'Daily KPI MRB,SPC', 'contactName' => 'kpi_ys_report_emails', 'currency' => Rate::EUR, 'siteIds' => [Site::MRB, Site::SPC]],
        'send-out-percent-daily' => \app\back\modules\task\actions\send\OutPercentDailyTask::class,
        'send-out-percent-monthly' => \app\back\modules\task\actions\send\OutPercentMonthlyTask::class,
        'send-out-percent-weekly' => \app\back\modules\task\actions\send\OutPercentWeeklyTask::class,
        'send-payments-approve-ratio-ggbet' => ['class' => \app\back\modules\task\actions\send\PaymentsApproveRatioTask::class, 'siteIds' => [Site::GGB], 'contactName' => 'payments_approve_ratio_ggbet_report_emails', 'subject' => 'Payments approve ratio GGBET'],
        'send-payments-approve-ratio-vv' => ['class' => \app\back\modules\task\actions\send\PaymentsApproveRatioTask::class, 'siteIds' => [Site::VV], 'contactName' => 'payments_approve_ratio_vv_report_emails', 'subject' => 'Payments approve ratio VulkanVegas'],
        'send-payments-approve-ratio-nvc' => ['class' => \app\back\modules\task\actions\send\PaymentsApproveRatioTask::class, 'siteIds' => [Site::NVC], 'contactName' => 'payments_approve_ratio_nvc_report_emails', 'subject' => 'Payments approve ratio NV Casino'],
        'send-payments-approve-ratio-icg' => ['class' => \app\back\modules\task\actions\send\PaymentsApproveRatioTask::class, 'siteIds' => [Site::ICG], 'contactName' => 'payments_approve_ratio_icg_report_emails', 'subject' => 'Payments approve ratio IceCasino'],
        'send-payments-comparison-gi' => ['class' => \app\back\modules\task\actions\send\PaymentsComparisonTask::class, 'siteIds' => [Site::VV, Site::GGB, Site::ICG, Site::VERDE, Site::CSBET], 'contactName' => 'payments_comparison_gi_report_emails', 'titlePostfix' => ' (GI)'],
        'send-payments-comparison-smen' => ['class' => \app\back\modules\task\actions\send\PaymentsComparisonTask::class, 'siteIds' => Site::PLATFORM_SITES_SMEN, 'contactName' => 'payments_comparison_smen_report_emails', 'titlePostfix' => ' (SMEN)'],
        'send-payments-fail-ratio' => ['class' => \app\back\modules\task\actions\send\PaymentsFailRatioTask::class, 'siteIds' => [Site::CV, Site::RC, Site::V24, Site::JC, Site::ONX, Site::PHB, Site::ARM, Site::V777, Site::MS, Site::S7, Site::VV, Site::ICG, Site::VERDE, Site::HIT], 'compareInterval' => '20 days',],
        'send-revenue-distribution-ggb' => ['class' => \app\back\modules\task\actions\send\RevenueDistributionTask::class, 'contact' => 'revenue_distribution_ggb_report', 'subject' => 'GGBet revenue distribution', 'blocks' => ['GGbet UA' => [['site_id', [Site::GGUA]]], 'GG.bet all countries' => [['site_id', [Site::GGB]]], 'Total GG' => [['site_id', [Site::GGB, Site::GGUA]]]]],
        'send-revenue-distribution-vb' => ['class' => \app\back\modules\task\actions\send\RevenueDistributionTask::class, 'contact' => 'revenue_distribution_vb_report', 'subject' => 'VBet revenue distribution', 'blocks' => ['VBet' => [['site_id', [Site::VBET]]]]],
        'send-betting-summary-ggb' => ['class' => \app\back\modules\task\actions\send\BettingSummaryTask::class, 'contact' => 'betting_summary_ggb_report', 'subject' => 'GGBet betting summary', 'parts' => ['GG.bet' => [Site::GGB], 'All GGbets' => [Site::GGB]]],
        'send-betting-summary-vb' => ['class' => \app\back\modules\task\actions\send\BettingSummaryTask::class, 'contact' => 'betting_summary_vb_report', 'subject' => 'VBet betting summary', 'parts' => ['VBet' => [Site::VBET]]],
        'send-betting-summary-vsp' => ['class' => \app\back\modules\task\actions\send\BettingSummaryTask::class, 'contact' => 'betting_summary_vsp_report', 'subject' => 'VSP betting summary', 'parts' => ['VSP' => [Site::VSP]]],
        'send-stp-payments' => ['class' => \app\back\modules\task\actions\send\StpPaymentsTask::class, 'contact' => 'stp_payments', 'groups' => [
            'RU' => [
                ['group' => [Site::GROUP_GI, Site::GROUP_SMEN, Site::GROUP_GPD], 'exclude' => [Site::VBET, Site::VV, Site::V777]],
            ],
            'DE' => [
                ['include' => [Site::VV, Site::GGB], 'countries' => ['DE'], 'operator' => FilterOperators::IN],
            ],
            'UA' => [
                ['include' => [Site::VV, Site::GGB], 'countries' => ['UA'], 'operator' => FilterOperators::IN],
                ['include' => [Site::V777]],
            ],
            'Int. (other)' => [
                ['include' => [Site::VV, Site::GGB], 'countries' => ['DE', 'UA'], 'operator' => FilterOperators::NOT_IN],
            ],
            'Total' => [
                ['group' => [Site::GROUP_SMEN, Site::GROUP_GPD, Site::GROUP_GI, Site::GROUP_GGB]],
            ],
        ]],
        'send-stp-payments-month' => ['class' => \app\back\modules\task\actions\send\StpPaymentsMonthTask::class, 'contact' => 'stp_payments_month'],
        'send-s2p-approve-ratio' => \app\back\modules\task\actions\send\S2PApproveRatioTask::class,
        'send-s2p-approve-ratio-banks' => \app\back\modules\task\actions\send\S2PApproveRatioBanksTask::class,
        'send-s2p-approve-ratio-ggbet-week' => \app\back\modules\task\actions\send\S2PApproveRatioGgbetWeekTask::class,
        'send-users-bonuses-percent-smen' => ['class' => \app\back\modules\task\actions\send\UsersBonusesPercentTask::class, 'contactName' => 'users_bonuses_percent_smen', 'siteIds' => array_diff(Site::PLATFORM_SITES_SMEN, [Site::S7, Site::AWI]), 'subjectPrefix' => 'CIS'],
        'send-users-bonuses-percent-gi' => ['class' => \app\back\modules\task\actions\send\UsersBonusesPercentTask::class, 'contactName' => 'users_bonuses_percent_gi', 'siteIds' => array_diff(Site::PLATFORM_SITES_GI, [Site::SLTR]), 'subjectPrefix' => 'GI (exclude Slotoro)'],
        'send-users-bonuses-percent-s7-awi' => ['class' => \app\back\modules\task\actions\send\UsersBonusesPercentTask::class, 'contactName' => 'users_bonuses_percent_s7_awi', 'siteIds' => [Site::S7, Site::AWI], 'groupByBrands' => true, 'subjectPrefix' => 'S7, AWI'],
        'send-users-deps-daily' => ['class' => \app\back\modules\task\actions\send\UsersDepTask::class, 'contact' => 'users_deps_v777', 'subject' => "Daily deposits", 'currency' => Rate::UAH, 'depThreshold' => 20_000, 'statuses' => [User::STATUS_VIP, User::STATUS_ASP, User::STATUS_ULTRA], 'activeStatuses' => [User::ACTIVE_STATUS_ACTIVE, User::ACTIVE_STATUS_NEW], 'siteIds' => [Site::V777]],
        'send-users-deps-weekly' => ['class' => \app\back\modules\task\actions\send\UsersDepTask::class, 'contact' => 'users_deps_v777', 'subject' => "Weekly deposits", 'currency' => Rate::UAH, 'depThreshold' => 28_000, 'siteIds' => [Site::V777]],
        'send-users-deps-top-daily-cis' => ['class' => \app\back\modules\task\actions\send\UsersDepsTopDailyTask::class, 'contact' => 'users_deps_top_cis', 'subject' => 'Top 10 CIS', 'siteIds' => ['group' => [Site::GROUP_SMEN, Site::GROUP_GPD, Site::GROUP_GI]], 'international' => false],
        'send-users-deps-top-daily-int' => ['class' => \app\back\modules\task\actions\send\UsersDepsTopDailyTask::class, 'contact' => 'users_deps_top_int', 'subject' => 'Top 20 INT', 'siteIds' => ['include' => [Site::VV, Site::GGB, Site::VBET, Site::ICG, Site::VERDE, Site::HIT, Site::NVC, Site::VSP]], 'international' => true, 'topSize' => 20],
        'send-users-games-comparison' => ['class' => \app\back\modules\task\actions\send\UsersGamesComparisonTask::class, 'siteGroups' => [Site::GROUP_SMEN, Site::GROUP_GPD, Site::GROUP_GI, Site::GROUP_GGB], 'contactName' => 'daily_games_report'],
        'send-trustly-users-info-mo-th' => ['class' => \app\back\modules\task\actions\send\TrustlyUsersTask::class, 'depInterval' => '4 day', 'sites' => [Site::VV, Site::GGB, Site::VBET], 'countries' => ['NO', 'AT', 'DE', 'PL', 'SE']],
        'send-trustly-users-info-fr-su' => ['class' => \app\back\modules\task\actions\send\TrustlyUsersTask::class, 'depInterval' => '3 day', 'sites' => [Site::VV, Site::GGB, Site::VBET], 'countries' => ['NO', 'AT', 'DE', 'PL', 'SE']],
        'send-vbet-daily-profit' => ['class' => \app\back\modules\task\actions\send\BettingDailyProfitTask::class, 'contact' => 'vbet_daily_profit', 'subject' => 'Vbet Daily Profit', 'siteIds' => [Site::VBET], 'currencies' => [Rate::EUR, Rate::RUB]],
        'send-vsp-daily-profit' => ['class' => \app\back\modules\task\actions\send\BettingDailyProfitTask::class, 'contact' => 'vsp_daily_profit', 'subject' => 'Vsp Daily Profit', 'siteIds' => [Site::VSP], 'currencies' => [Rate::EUR, Rate::RUB]],
        'send-top-pay-country-ptg-ggbet-week' => ['class' => \app\back\modules\task\actions\send\TopPayCountryPtgWeekTask::class, 'siteId' => Site::GGB, 'contactName' => 'top_pay_country_ptg_ggbet_week_report_emails', 'interval' => '7 day', 'subject' => 'weekly WD rate stats'],
        'send-top-pay-country-ptg-ggbet-month' => ['class' => \app\back\modules\task\actions\send\TopPayCountryPtgWeekTask::class, 'siteId' => Site::GGB, 'contactName' => 'top_pay_country_ptg_ggbet_week_report_emails', 'interval' => '1 month', 'subject' => 'monthly WD rate stats'],
        'send-speed-out-transactions' => ['class' => \app\back\modules\task\actions\send\SpeedOutTransactionsTask::class, 'contactName' => 'speed_out_transactions_report_emails'],
        'send-users-games-statistics-int' => ['class' => \app\back\modules\task\actions\send\UsersGamesStatisticsTask::class, 'siteIds' => [Site::GGB, Site::VV, Site::ICG, Site::VERDE, Site::VBET, Site::SLTR, Site::AWI, Site::S7, Site::HIT, Site::VP], 'contactName' => 'users_games_statistics_report_emails_int'],
        'send-users-games-statistics-cis' => ['class' => \app\back\modules\task\actions\send\UsersGamesStatisticsTask::class, 'siteIds' => [Site::CV, Site::ARM, Site::PHB, Site::JC, Site::VS, Site::RC, Site::V24, Site::EL, Site::ONX, Site::GMS, Site::GMSD, Site::SZL, Site::RUBIN, Site::MS, Site::K7], 'contactName' => 'users_games_statistics_report_emails_cis'],
        'send-mobile-apps-funnel' => ['class' => \app\back\modules\task\actions\send\MobileAppsFunnelTask::class, 'siteIds' => array_diff(Site::PLATFORM_SITES_SMEN, [Site::LOS, Site::VS]), 'contactName' => 'mobile_apps_funnel_report_emails', 'subject' => 'SMEN Mobile Apps Funnel Weekly'],
        'send-mobile-apps-funnel-gi' => ['class' => \app\back\modules\task\actions\send\MobileAppsFunnelTask::class, 'siteIds' => [Site::VV, Site::ICG, Site::VERDE, Site::VBET, Site::HIT, Site::SLTR], 'contactName' => 'mobile_apps_funnel_report_emails', 'subject' => 'GI Mobile Apps Funnel Weekly', 'colGroups' => [['', 1],['Native App', 4],['Web View', 2]]],
        'send-mobile-apps-funnel-cg' => ['class' => \app\back\modules\task\actions\send\MobileAppsFunnelTask::class, 'siteIds' => [Site::LOS, Site::VS], 'contactName' => 'mobile_apps_funnel_report_emails', 'subject' => 'CG Mobile Apps Funnel Weekly'],
        'send-users-reg-proton' => ['class' => \app\back\modules\task\actions\send\UsersRegProtonTask::class, 'contact' => 'users_reg_proton_emails'],
        'send-fd-dynamic' => ['class' => \app\back\modules\task\actions\send\DynamicFdTask::class, 'siteIds' => [Site::VV, Site::ICG, Site::VERDE, Site::S7, Site::HIT, Site::NVC, Site::VOX], 'contact' => 'fd_dynamic_emails'],
        'send-fd-dynamic-vp' => ['class' => \app\back\modules\task\actions\send\DynamicFdVpTask::class, 'siteIds' => [Site::VV, Site::ICG, Site::VERDE, Site::S7, Site::HIT, Site::GGB, Site::GGUA, Site::AWI, Site::SLTR, Site::VBET, Site::VSP], 'contact' => 'fd_dynamic_vp_emails'],
        'send-rokeente-tags' => ['class' => \app\back\modules\task\actions\send\RokeenteTagsTask::class, 'contact' => 'rokeente_tags_emails'],
        'send-deps-metrics-daily' => ['class' => \app\back\modules\task\actions\send\DepsMetricsTask::class, 'contact' => 'deps_metrics_emails', 'siteIds' => [Site::GGUA], 'depInterval' => '15 day', 'groupBy' => 'day', 'subject' => 'Daily GGbet.ua Deps'],
        'send-deps-metrics-weekly' => ['class' => \app\back\modules\task\actions\send\DepsMetricsTask::class, 'contact' => 'deps_metrics_emails', 'siteIds' => [Site::GGUA], 'depInterval' => '30 day', 'groupBy' => 'week', 'subject' => 'Weekly GGbet.ua Deps'],
        'send-out-percent-by-country' => ['class' => \app\back\modules\task\actions\send\OutPercentByCountryTask::class, 'contact' => 'out_percent_by_country_emails', 'siteIds' => [Site::VV, Site::ICG, Site::VERDE, Site::HIT, Site::NVC]],
        'send-s2p-notify-documents-wait-cis' => ['class' => \app\back\modules\task\actions\send\S2pNotifyDocumentsWaitTask::class, 'contact' => 'documents_verification_wait_cis', 'isCisSites' => true, 'subject' => 'User verification wait more than 24 hours CIS'],
        'send-s2p-notify-documents-wait-int' => ['class' => \app\back\modules\task\actions\send\S2pNotifyDocumentsWaitTask::class, 'contact' => 'documents_verification_wait_int', 'isCisSites' => false, 'exclude' => [Site::GGB, Site::ICG, Site::VV], 'subject' => 'User verification wait more than 24 hours INT'],
        'send-new-canonical-pay-sys' => ['class' => \app\back\modules\task\actions\send\NewCanonicalPaySysTask::class, 'contact' => 'new_canonical_pay_sys_emails'],
        'send-users-games-casino-weekly-ggb' => ['class' => \app\back\modules\task\actions\send\UsersGamesCasinoWeeklyTask::class, 'contact' => 'users_games_casino_weekly_emails', 'siteIds' => [Site::GGB], 'titlePostfix' => 'GGB', 'titlePrefix' => 'GG Product.'],
        'send-users-games-casino-weekly-ggua' => ['class' => \app\back\modules\task\actions\send\UsersGamesCasinoWeeklyTask::class, 'contact' => 'users_games_casino_weekly_emails', 'siteIds' => [Site::GGUA], 'titlePostfix' => 'GGUA', 'titlePrefix' => 'GG Product.'],
        'send-checkbox-receipts' => ['class' => \app\back\modules\task\actions\send\CheckboxReceiptsTask::class, 'contact' => 'checkbox_receipts_emails', 'siteIds' => [Site::GGUA]],
        'send-vip-probability-cis' => ['class' => \app\back\modules\task\actions\send\VipProbabilityTask::class, 'contact' => 'vip_probability_cis_emails', 'titlePostfix' => 'CIS', 'countries' => ['RU'], 'siteIds' => [Site::GMS, Site::CV, Site::GMSD, Site::RUBIN, Site::ARM, Site::PHB, Site::GIV, Site::VIP, Site::EL, Site::V24, Site::VS, Site::JC, Site::ONX, Site::RC, Site::MS, Site::SZL, Site::K7, Site::VP]],
        'send-vip-probability-int' => ['class' => \app\back\modules\task\actions\send\VipProbabilityTask::class, 'contact' => 'vip_probability_int_emails', 'titlePostfix' => 'INT', 'countries' => ['IT', 'PL', 'DE'], 'siteIds' => [Site::VV, Site::ICG, Site::VERDE, Site::VBET, Site::HIT, Site::NVC]],
        'send-vip-probability-upi' => ['class' => \app\back\modules\task\actions\send\VipProbabilityTask::class, 'contact' => 'vip_probability_upi_emails', 'titlePostfix' => 'UPI', 'countries' => ['TR', 'IT', 'PL'], 'siteIds' => [Site::S7, Site::LOS, Site::AWI, Site::SLTR, Site::VOX, Site::WIN]],
        'send-deposits-failed' => ['class' => \app\back\modules\task\actions\send\DepositsFailedTask::class, 'contact' => 'deposits_failed_emails'],
    ],
];
