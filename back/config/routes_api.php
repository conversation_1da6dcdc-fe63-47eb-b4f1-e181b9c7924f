<?php

declare(strict_types=1);

return [
    'amp' => [
        'get' => [
            \app\back\modules\api\clients\BrandsMethod::class,
            \app\back\modules\api\clients\SitesMethod::class,
            \app\back\modules\api\clients\amp\GameActivityMethod::class,
            \app\back\modules\api\clients\amp\LoginsMethod::class,
            \app\back\modules\api\clients\amp\LyraUsersMethod::class,
            \app\back\modules\api\clients\amp\PromoCodesMethod::class,
            \app\back\modules\api\clients\amp\TransactionsMethod::class,
            \app\back\modules\api\clients\amp\UsersMethod::class,
        ],
    ],
    'crm' => [
        'get' => [
            /* Filters */
            \app\back\modules\api\clients\crm\filter\ActionsMethod::class,
            \app\back\modules\api\clients\crm\filter\BettingMethod::class,
            \app\back\modules\api\clients\crm\filter\BonusesActivationsMethod::class,
            \app\back\modules\api\clients\crm\filter\BonusesMethod::class,
            \app\back\modules\api\clients\crm\filter\ContactsMethod::class,
            \app\back\modules\api\clients\crm\filter\FeaturesMethod::class,
            \app\back\modules\api\clients\crm\filter\GamesChosenMethod::class,
            \app\back\modules\api\clients\crm\filter\GamesMaxWinMethod::class,
            \app\back\modules\api\clients\crm\filter\GamesMethod::class,
            \app\back\modules\api\clients\crm\filter\GinPlayersListMethod::class,
            \app\back\modules\api\clients\crm\filter\GinSearchPlayersMethod::class,
            \app\back\modules\api\clients\crm\filter\LoginsMethod::class,
            \app\back\modules\api\clients\crm\filter\LootboxesMethod::class,
            \app\back\modules\api\clients\crm\filter\LoyaltyMethod::class,
            \app\back\modules\api\clients\crm\filter\MetricsMethod::class,
            \app\back\modules\api\clients\crm\filter\NetRevenueMethod::class,
            \app\back\modules\api\clients\crm\filter\PaymentsActivityMethod::class,
            \app\back\modules\api\clients\crm\filter\PaymentsMethod::class,
            \app\back\modules\api\clients\crm\filter\RebillsMethod::class,
            \app\back\modules\api\clients\crm\filter\SplitTestsMethod::class,
            \app\back\modules\api\clients\crm\filter\TournamentsMethod::class,
            \app\back\modules\api\clients\crm\filter\UserBlocksMethod::class,
            \app\back\modules\api\clients\crm\filter\UsersListMethod::class,
            \app\back\modules\api\clients\crm\filter\UsersMethod::class,

            /* Info */
            \app\back\modules\api\clients\crm\info\InfoContactsMethod::class,
            \app\back\modules\api\clients\crm\info\InfoDevicesMethod::class,
            \app\back\modules\api\clients\crm\info\InfoFavoriteGamesMethod::class,
            \app\back\modules\api\clients\crm\info\InfoLoginsMethod::class,
            \app\back\modules\api\clients\crm\info\InfoLoyaltyMethod::class,
            \app\back\modules\api\clients\crm\info\InfoMaxWinMethod::class,
            \app\back\modules\api\clients\crm\info\InfoMethod::class,
            \app\back\modules\api\clients\crm\info\InfoMetricsMethod::class,
            \app\back\modules\api\clients\crm\info\InfoPaymentsMethod::class,
            \app\back\modules\api\clients\crm\info\InfoTournamentsMethod::class,
            \app\back\modules\api\clients\crm\info\InfoUsersGamesTopWinsMethod::class,
            \app\back\modules\api\clients\crm\info\InfoStatisticsUsersMethod::class,
            \app\back\modules\api\clients\crm\info\InfoSplitUsersGroups::class,
            \app\back\modules\api\clients\crm\info\InfoBonusOfferMethod::class,
            \app\back\modules\api\clients\crm\info\InfoLoginPlatformsMethod::class,

            /* Dictionaries */
            \app\back\modules\api\clients\crm\dict\DictBettingBetStatusesMethod::class,
            \app\back\modules\api\clients\crm\dict\DictBettingBetTypesMethod::class,
            \app\back\modules\api\clients\crm\dict\DictBettingEventStatusesMethod::class,
            \app\back\modules\api\clients\crm\dict\DictBettingOddStatusesMethod::class,
            \app\back\modules\api\clients\crm\dict\DictBettingSportsMethod::class,
            \app\back\modules\api\clients\crm\dict\DictBettingTournamentsMethod::class,
            \app\back\modules\api\clients\crm\dict\DictBonusPrizeTypesMethod::class,
            \app\back\modules\api\clients\crm\dict\DictBonusesMethod::class,
            \app\back\modules\api\clients\crm\dict\DictBrandsMethod::class,
            \app\back\modules\api\clients\crm\dict\DictBrowsersMethod::class,
            \app\back\modules\api\clients\crm\dict\DictCountriesMethod::class,
            \app\back\modules\api\clients\crm\dict\DictCrmChannelsMethod::class,
            \app\back\modules\api\clients\crm\dict\DictCrmGroupsMethod::class,
            \app\back\modules\api\clients\crm\dict\DictDevicesMethod::class,
            \app\back\modules\api\clients\crm\dict\DictGamesMethod::class,
            \app\back\modules\api\clients\crm\dict\DictGamesVendorsMethod::class,
            \app\back\modules\api\clients\crm\dict\DictLocalesMethod::class,
            \app\back\modules\api\clients\crm\dict\DictLootboxPrizeTypesMethod::class,
            \app\back\modules\api\clients\crm\dict\DictLoyaltyPrizesMethod::class,
            \app\back\modules\api\clients\crm\dict\DictLoyaltyStatusesMethod::class,
            \app\back\modules\api\clients\crm\dict\DictOperatorsMethod::class,
            \app\back\modules\api\clients\crm\dict\DictPaymentDirectionsMethod::class,
            \app\back\modules\api\clients\crm\dict\DictPaymentStatusesMethod::class,
            \app\back\modules\api\clients\crm\dict\DictPaymentExtTypesMethod::class,
            \app\back\modules\api\clients\crm\dict\DictPaymentSystemsMethod::class,
            \app\back\modules\api\clients\crm\dict\DictPaymentSystemsS2pMethod::class,
            \app\back\modules\api\clients\crm\dict\DictPlatformsMethod::class,
            \app\back\modules\api\clients\crm\dict\DictRatesMethod::class,
            \app\back\modules\api\clients\crm\dict\DictSitesMethod::class,
            \app\back\modules\api\clients\crm\dict\DictSocialNetsMethod::class,
            \app\back\modules\api\clients\crm\dict\DictTournamentsMethod::class,
            \app\back\modules\api\clients\crm\dict\DictTrafficSourcesMethod::class,
            \app\back\modules\api\clients\crm\dict\DictUserActiveStatusesMethod::class,
            \app\back\modules\api\clients\crm\dict\DictUseragentPlatformGroupsMethod::class,
            \app\back\modules\api\clients\crm\dict\DictUserBlockTypesMethod::class,
            \app\back\modules\api\clients\crm\dict\DictUserBlockReasonsMethod::class,
            \app\back\modules\api\clients\crm\dict\DictUserBlockSourcesMethod::class,
            \app\back\modules\api\clients\crm\dict\DictUserSegmentsMethod::class,
            \app\back\modules\api\clients\crm\dict\DictUserStatusesMethod::class,
            \app\back\modules\api\clients\crm\dict\DictUseragentAppGroupsMethod::class,
            \app\back\modules\api\clients\crm\dict\DictUseragentAppPlatformsMethod::class,
            \app\back\modules\api\clients\crm\dict\DictVariantsMethod::class,
            \app\back\modules\api\clients\crm\dict\DictVipaffAffSourcesMethod::class,

            /* Custom actions */
            'betting-tournaments' => \app\back\modules\api\clients\crm\dict\DictBettingTournamentsMethod::class, /* @DEPRECATED here */
            \app\back\modules\api\clients\crm\ContactsResolveMethod::class,
            \app\back\modules\api\clients\crm\LoginsStatsMethod::class,
            \app\back\modules\api\clients\crm\PaymentsStatsMethod::class,
            \app\back\modules\api\clients\crm\SubscriptionsMethod::class,
            \app\back\modules\api\clients\crm\ContactForProcessingMethod::class,

            /* Batch */
            \app\back\modules\api\clients\crm\BatchMethod::class,
        ],
        'put' => [
            'users-contacts' => \app\back\modules\api\clients\crm\PutUsersContactsMethod::class,
        ]

    ],
    'ginaf' => [
        'get' => [
            \app\back\modules\api\clients\ginaf\GameSessionsMethod::class,
            \app\back\modules\api\clients\ginaf\TransactionsMethod::class,
            \app\back\modules\api\clients\ginaf\UsersDocumentsMethod::class,
            \app\back\modules\api\clients\ginaf\UsersMethod::class,
        ],
    ],
    'marketing' => [
        'get' => [
            \app\back\modules\api\clients\SitesMethod::class, //Access Product
            \app\back\modules\api\clients\marketing\RevenueLoginMethod::class, //DBM Login Revenue Stats
            \app\back\modules\api\clients\marketing\SummaryMethod::class, // DBM Stats
            \app\back\modules\api\clients\marketing\TrafficSourcesRulesMethod::class, //DBM Traffic Source Registration
            \app\back\modules\api\clients\marketing\UserRequisitesHistoryMethod::class, //PP Antifraud
            \app\back\modules\api\clients\marketing\UsersActionsMethod::class, //DMP
            \app\back\modules\api\clients\marketing\UserInfoMethod::class, //Antifraud @Starf
            \app\back\modules\api\clients\marketing\TransactionInfoMethod::class,  //Antifraud @Starf
        ],
    ],
    'netent' => [
        'get' => [
            \app\back\modules\api\clients\netent\OwnHostMethod::class
        ],
    ],
    's2p' => [
        'get' => [
            \app\back\modules\api\clients\s2p\InvoiceStatusMethod::class,
            \app\back\modules\api\clients\s2p\RefTypeRulesMethod::class,
            \app\back\modules\api\clients\s2p\RefcodesMethod::class,
            \app\back\modules\api\clients\s2p\TestUsersMethod::class,
            \app\back\modules\api\clients\s2p\TrafficSourcesMethod::class,
            \app\back\modules\api\clients\s2p\TrafficSourcesRulesMethod::class,
            \app\back\modules\api\clients\s2p\UserCidStatsMethod::class,
            \app\back\modules\api\clients\s2p\UsersChargebackMethod::class,
            \app\back\modules\api\clients\s2p\UsersDepositsLtMethod::class,
            \app\back\modules\api\clients\s2p\UsersDocumentsMethod::class,
            \app\back\modules\api\clients\s2p\UsersIsBlockedMethod::class,
            \app\back\modules\api\clients\s2p\UsersKycMethod::class,
            \app\back\modules\api\clients\s2p\UsersLoginsMethod::class,
            \app\back\modules\api\clients\s2p\UsersMethod::class,
            \app\back\modules\api\clients\s2p\UsersRefcodesMethod::class,
            \app\back\modules\api\clients\s2p\UsersScoreMethod::class,
            \app\back\modules\api\clients\s2p\UsersVerificationProgressMethod::class,
            \app\back\modules\api\clients\s2p\VipUsersMethod::class,
        ],
        'put' => [
            'users-kyc' => \app\back\modules\api\clients\s2p\PutUsersKycMethod::class,
        ]
    ],
    'stp' => [
        'get' => [
            \app\back\modules\api\clients\SitesMethod::class,
            \app\back\modules\api\clients\stp\PaymentsMethod::class,
            \app\back\modules\api\clients\stp\UsersMethod::class,
        ],
    ],
    'smen' => [
        'get' => [
            \app\back\modules\api\clients\smen\GamesRatingMethod::class,
            \app\back\modules\api\clients\smen\UsersChargebackMethod::class,
            \app\back\modules\api\clients\smen\UsersGamesRecommendMethod::class,
            \app\back\modules\api\clients\smen\UsersStatusesMethod::class,
            \app\back\modules\api\clients\smen\UsersTicketsLogMethod::class,
            \app\back\modules\api\clients\smen\UsersTicketsMethod::class,
            \app\back\modules\api\clients\smen\UsersVerificationStatusesMethod::class,
        ],
    ],
    'vipaff' => [
        'get' => [
            \app\back\modules\api\clients\BrandsMethod::class,
            \app\back\modules\api\clients\SitesMethod::class,
            \app\back\modules\api\clients\vipaff\GameActivityMethod::class,
            \app\back\modules\api\clients\vipaff\TransactionsMethod::class,
            \app\back\modules\api\clients\vipaff\UsersMethod::class,
            \app\back\modules\api\clients\vipaff\VisitsMethod::class,
        ],
    ],
    /** clone of vipAff */
    'creamPartners' => [
        'get' => [
            \app\back\modules\api\clients\BrandsMethod::class,
            \app\back\modules\api\clients\SitesMethod::class,
            \app\back\modules\api\clients\vipaff\GameActivityMethod::class,
            \app\back\modules\api\clients\vipaff\TransactionsMethod::class,
            \app\back\modules\api\clients\vipaff\UsersMethod::class,
            \app\back\modules\api\clients\vipaff\VisitsMethod::class,
        ],
    ],
    'wp3' => [
        'get' => [
            \app\back\modules\api\clients\BrandsMethod::class,
            \app\back\modules\api\clients\SitesMethod::class,
            \app\back\modules\api\clients\wp3\BalancesMethod::class,
            \app\back\modules\api\clients\wp3\BetsMethod::class,
            \app\back\modules\api\clients\wp3\CidsMethod::class,
            \app\back\modules\api\clients\wp3\CidsTotalsMethod::class,
            \app\back\modules\api\clients\wp3\GameActivityMethod::class,
            \app\back\modules\api\clients\wp3\LoginsMethod::class,
            \app\back\modules\api\clients\wp3\MultiAccountsMethod::class,
            \app\back\modules\api\clients\wp3\TransactionsMethod::class,
            \app\back\modules\api\clients\wp3\UsersMethod::class,
            \app\back\modules\api\clients\wp3\VisitsLiveMethod::class,
            \app\back\modules\api\clients\wp3\VisitsMethod::class,
        ],
    ],
    'ampAnalytics' => [
        'get' => [
            \app\back\modules\api\clients\wp3\CidsTotalsMethod::class,
            \app\back\modules\api\clients\wp3\LoginsMethod::class,
            \app\back\modules\api\clients\wp3\TransactionsMethod::class,
            \app\back\modules\api\clients\wp3\UsersMethod::class,
            \app\back\modules\api\clients\SitesMethod::class,
        ],
    ],
    'yhelper' => [
        'get' => [
            \app\back\modules\api\clients\BrandsMethod::class,
            \app\back\modules\api\clients\yhelper\CrmOperatorCommentMethod::class,
            \app\back\modules\api\clients\yhelper\OperatorsInfoMethod::class,
            \app\back\modules\api\clients\yhelper\RatesMethod::class,
            \app\back\modules\api\clients\yhelper\UserCidLastActivityMethod::class,
            \app\back\modules\api\clients\yhelper\UserCidsMethod::class,
            \app\back\modules\api\clients\yhelper\UserCidsTotalMethod::class,
            \app\back\modules\api\clients\yhelper\UserInfoForExportMethod::class,
            \app\back\modules\api\clients\yhelper\UserInfoMethod::class,
            \app\back\modules\api\clients\yhelper\UserStatsMethod::class,
            \app\back\modules\api\clients\yhelper\UserTransactionsMethod::class,
            \app\back\modules\api\clients\yhelper\UsersBirthdaysMethod::class,
            \app\back\modules\api\clients\yhelper\UsersContactsMethod::class,
            \app\back\modules\api\clients\yhelper\UsersForOnlineCheckMethod::class,
            \app\back\modules\api\clients\yhelper\UsersTicketsMethod::class,
            \app\back\modules\api\clients\yhelper\UsersTypeSearchMethod::class,
            \app\back\modules\api\clients\yhelper\UsersWalletsMethod::class,
        ],
        'put' => [
            'manual-payment' => \app\back\modules\api\clients\yhelper\PutManualPaymentMethod::class,
            'users-contacts' => \app\back\modules\api\clients\yhelper\PutUsersContactsMethod::class,
            'users-info' => \app\back\modules\api\clients\yhelper\PutUsersInfoMethod::class,
            'users-metrics' => \app\back\modules\api\clients\yhelper\PutUsersMetricsMethod::class,
            'users-timezone' => \app\back\modules\api\clients\yhelper\PutUsersTimezoneMethod::class,
            'withdrawal-approve' => \app\back\modules\api\clients\yhelper\PutWithdrawalApproveMethod::class,
            'users-score' => \app\back\modules\api\clients\yhelper\PutUsersScoreMethod::class,
        ],
        'delete' => [
            'users-contacts' => \app\back\modules\api\clients\yhelper\DeleteUsersContactsMethod::class,
        ]
    ],
    'fin' => [
        'get' => [
            \app\back\modules\api\clients\fin\TransactionsMethod::class
        ]
    ]
];
