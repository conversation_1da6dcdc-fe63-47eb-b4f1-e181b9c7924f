<?php

declare(strict_types=1);

namespace app\back\migrations;

class m250702_065308_lottery_wins_table_expanded_from_json_to_columns extends BaseMigration
{
    private const string LOTTERY_WINS = 'lottery_wins';

    public function safeUp(): void
    {
        $this->db->createCommand()->addColumn(self::LOTTERY_WINS, 'win_place', 'smallint')->execute();
        $this->db->createCommand()->addColumn(self::LOTTERY_WINS, 'currency', 'varchar(5)')->execute();
        $this->db->createCommand()->addColumn(self::LOTTERY_WINS, 'win_amount', 'numeric(20, 2)')->execute();
        $this->db->createCommand()->addColumn(self::LOTTERY_WINS, 'rate_usd', 'numeric(20, 10)')->execute();
        $this->db->createCommand()->addColumn(self::LOTTERY_WINS, 'rate_eur', 'numeric(20, 10)')->execute();
    }

    public function safeDown(): void
    {
        $this->db->createCommand()->dropColumn(self::LOTTERY_WINS, 'win_place')->execute();
        $this->db->createCommand()->dropColumn(self::LOTTERY_WINS, 'currency')->execute();
        $this->db->createCommand()->dropColumn(self::LOTTERY_WINS, 'win_amount')->execute();
        $this->db->createCommand()->dropColumn(self::LOTTERY_WINS, 'rate_usd')->execute();
        $this->db->createCommand()->dropColumn(self::LOTTERY_WINS, 'rate_eur')->execute();
    }
}
