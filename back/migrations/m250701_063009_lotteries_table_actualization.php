<?php

declare(strict_types=1);

namespace app\back\migrations;

class m250701_063009_lotteries_table_actualization extends BaseMigration
{
    private const string LOTTERY_TABLE = 'lotteries';

    public function safeUp(): void
    {
        $this->db->createCommand()->dropColumn(self::LOTTERY_TABLE, 'info')->execute();
        $this->db->createCommand()->addColumn(self::LOTTERY_TABLE, 'is_prizes_distributed', 'boolean')->execute();
    }

    public function safeDown(): void
    {
        $this->db->createCommand()->addColumn(self::LOTTERY_TABLE, 'info', 'jsonb')->execute();
        $this->db->createCommand()->dropColumn(self::LOTTERY_TABLE, 'is_prizes_distributed')->execute();
    }
}
