<?php

declare(strict_types=1);

namespace app\back\migrations;

class m250618_073616_new_entity_distribution_stat_table extends BaseMigration
{
    private const string TABLE_NAME = 'lyra_stream_atypical_distribution_stat';

    public function safeUp(): void
    {
        $this->db->createCommand()->dropTable(self::TABLE_NAME)->execute();

        $this->db->createCommand()->createTable(self::TABLE_NAME, [
            'webmaster_id' => 'varchar(32) not null',
            'site_id' => 'integer not null',
            'distribution_types' => 'integer[] not null',
            'entity' => 'text[] not null',
            'entities_count' => 'integer not null',
            'entity_stream_size' => 'integer not null',
            'entity_stream_percent' => 'numeric(10,2) not null',
            'updated_at' => 'timestamp not null default now()',
        ])->execute();

        $this->db->createCommand()->addPrimaryKey(
            self::TABLE_NAME,
            self::TABLE_NAME . '_pk',
            ['webmaster_id', 'site_id', 'distribution_types', 'entity']
        )->execute();

        $this->db->createCommand()->createIndex(
            self::TABLE_NAME,
            self::TABLE_NAME . '_updated_at_entity_type_idx',
            ['updated_at', 'distribution_types']
        );
    }

    public function safeDown(): void
    {
        $this->db->createCommand()->dropTable(self::TABLE_NAME)->execute();

        $this->db->createCommand()->createTable(self::TABLE_NAME, [
            'webmaster_id' => 'varchar(32) not null',
            'site_id' => 'integer not null',
            'entity_type' => 'integer not null',
            'entity_id' => 'integer not null',
            'entities_count' => 'integer not null',
            'entity_stream_size' => 'integer not null',
            'entity_stream_percent' => 'numeric(10,2) not null',
            'updated_at' => 'timestamp not null default now()',
        ])->execute();

        $this->db->createCommand()->addPrimaryKey(
            self::TABLE_NAME,
            self::TABLE_NAME . '_pk',
            ['webmaster_id', 'site_id', 'entity_type', 'entity_id']
        )->execute();

        $this->db->createCommand()->createIndex(
            self::TABLE_NAME,
            self::TABLE_NAME . '_updated_at_entity_type_idx',
            ['updated_at', 'entity_type']
        );
    }
}
