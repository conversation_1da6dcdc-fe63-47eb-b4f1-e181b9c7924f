<?php

declare(strict_types=1);

namespace app\back\repositories;

use app\back\entities\LyraStreamAtypicalDistributionStat;
use app\back\repositories\Cities;
use app\back\repositories\RokeenteSessions;
use app\back\repositories\RokeenteSessionUsers;
use app\back\repositories\UseragentDevices;
use app\back\repositories\Useragents;

class LyraStreamAtypicalDistributionStats extends BaseRepository
{
    public const string ENTITY_CLASS = LyraStreamAtypicalDistributionStat::class;
    public const string TABLE_NAME = 'lyra_stream_atypical_distribution_stat';
    public const array PRIMARY_KEY = ['webmaster_id', 'site_id', 'entity_type', 'entity_id'];

    public const string VALUE_ND = 'n/d';

    public static function getFieldsAndJoinsByDistributionTypes(array $distributionTypes, string $usersTableName = 'u'): array
    {
        $fieldsInDistribution = [];
        $additionalTables = [];

        $tablesToJoinMap = [
            'rsu' => [['rsu' => RokeenteSessionUsers::TABLE_NAME], "{$usersTableName}.user_id = rsu.user_id and {$usersTableName}.site_id = rsu.site_id"],
            'rs' => [['rs' => RokeenteSessions::TABLE_NAME], 'rs.session_id = rsu.session_id'],
            'ua' => [['ua' => UserAgents::TABLE_NAME], 'rs.server_useragent_id = ua.id'],
            'ua_d' => [['ua_d' => UseragentDevices::TABLE_NAME], 'ua_d.id = ua.device_id'],
            'c' => [['c' => Cities::TABLE_NAME], "c.id = {$usersTableName}.city_id"]
        ];

        foreach ($distributionTypes as $distributionType) {
            switch ($distributionType) {
                case LyraStreamAtypicalDistributionStat::ENTITY_TYPE_UA_DEVICE_DISTRIBUTION:
                    $fieldsInDistribution[$distributionType] = 'ua_d.name';
                    $additionalTables[] = 'rs';
                    $additionalTables[] = 'rsu';
                    $additionalTables[] = 'ua';
                    $additionalTables[] = 'ua_d';
                    break;
                case LyraStreamAtypicalDistributionStat::ENTITY_TYPE_WEBGL_RENDERER_DISTRIBUTION:
                    $fieldsInDistribution[$distributionType] = "rs.info->>'unmasked_renderer'";
                    $additionalTables[] = 'rs';
                    $additionalTables[] = 'rsu';
                    break;
                case LyraStreamAtypicalDistributionStat::ENTITY_TYPE_REG_CITY_DISTRIBUTION:
                    $fieldsInDistribution[$distributionType] = 'c.name';
                    $additionalTables[] = 'c';
                    break;
                default:
                    throw new \RuntimeException('Unknown distribution type id: ' . $distributionType);
            }
        }


        $tablesJoinConfig = array_intersect_key($tablesToJoinMap, array_fill_keys($additionalTables, null));

        if (count(array_unique($additionalTables)) !== count($tablesJoinConfig)) {
            throw new \RuntimeException('Some tables are not found in $tablesToJoinMap config');
        }

        return [$fieldsInDistribution, $tablesJoinConfig];
    }

    public static function getDistributionTypesJoinField(array $entityTypes): string
    {
        sort($entityTypes);
        return '{' . implode(',', $entityTypes) . '}';
    }

    public static function getEntityJoinField(array $fields): string
    {
        ksort($fields);
        $coalesceFields = array_map(fn($field) => "coalesce({$field}, '" . self::VALUE_ND . "')", array_values($fields));
        return "ARRAY[" . implode(',', $coalesceFields) . "]";
    }
}
