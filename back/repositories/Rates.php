<?php

declare(strict_types=1);

namespace app\back\repositories;

use app\back\entities\Rate;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class Rates extends BaseRepository
{
    public const string ENTITY_CLASS = Rate::class;
    public const string TABLE_NAME = 'rates';
    public const array PRIMARY_KEY = ['code', 'date'];

    private array $ratesCache = [];

    public function getLast(string $code, ?string $datetime = null): float|int
    {
        if ($datetime === null) {
            $datetime = date('Y-m-d H:i:s');
        }

        $datetime = date('Y-m-d H:00:00', strtotime($datetime));

        $code = Rate::fixCurrency($code);

        $divider = 1;

        if (array_key_exists($code, Rate::PRESET_RATES)) {
            [$divider, $code] = Rate::PRESET_RATES[$code];
            if ($divider === 0) {
                return 0.0;
            }
        }

        if (!isset($this->ratesCache[$code][$datetime])) {
            $rate = (new Query($this->db))
                ->select('rate')
                ->from(static::TABLE_NAME)
                ->where(['<=', 'date', $datetime])
                ->andWhere(['code' => $code])
                ->orderBy(['date' => SORT_DESC])
                ->limit(1)
                ->scalar();

            if ($rate === false) {
                throw new \RuntimeException("Rate not found for $code on $datetime");
            }

            if (!isset($this->ratesCache[$code])) {
                $this->ratesCache[$code] = [];
            }

            $this->ratesCache[$code][$datetime] = $rate;
        }

        return $this->ratesCache[$code][$datetime] / $divider;
    }

    public function getCrossRate(string $from, string $to, ?string $datetime = null): float
    {
        if ($from === Rate::USD) {
            return $this->getLast($to, $datetime);
        }

        $rateFrom = $this->getLast($from, $datetime);
        if ($rateFrom === 0.0) {
            return 0.0;
        }

        if ($to === Rate::USD) {
            return 1 / $rateFrom;
        }

        $rate = $this->getLast($to, $datetime);

        return $rate / $rateFrom;
    }

    public function convert($sum, ?string $from, string $to, ?string $datetime = null, int $precision = 2): ?float
    {
        if ($sum === null || $from === null) {
            return null;
        }

        if ($from === $to || empty($sum)) {
            return (float)$sum;
        }

        return round($sum * $this->getCrossRate($from, $to, $datetime), $precision);
    }

    public function exist(string $code): bool
    {
        static $rates;

        if (!isset($rates[$code])) {
            try {
                $this->getLast($code);
                $result = true;
            } catch (\RuntimeException) {
                $result = false;
            }
            $rates[$code] = $result;
        }

        return $rates[$code];
    }

    public static function ratesQuery(ConnectionInterface $db, string $dateColumn, string $fromCurrencyColumn, ?string $toCurrencyCode): Query
    {
        if ($toCurrencyCode === Rate::ORIG || $toCurrencyCode === null) {
            return (new Query($db))
                ->select('v.rate')
                ->from('(VALUES (1)) as v(rate)');
        }

        return (new Query($db))
            ->select(['rate' => "ANY_VALUE(r.rate) FILTER (WHERE r.code = :to_currency) / ANY_VALUE(r.rate) FILTER (WHERE r.code = $fromCurrencyColumn)"])
            ->from("(VALUES ($fromCurrencyColumn), (:to_currency)) as c (currency)")
            ->join('CROSS JOIN LATERAL', ['r' => (new Query($db))
                ->select(['r.rate', 'r.code'])
                ->from(['r' => self::TABLE_NAME])
                ->where("r.date < $dateColumn AND r.code = c.currency")
                ->orderBy(['r.date' => SORT_DESC])
                ->limit(1)])
            ->addParams([':to_currency' => $toCurrencyCode]);
    }
}
