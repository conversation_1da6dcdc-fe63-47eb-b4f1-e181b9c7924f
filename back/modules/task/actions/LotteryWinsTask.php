<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\entities\Rate;
use app\back\modules\task\ImportTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\BaseRepository;
use app\back\repositories\LotteryWins;
use app\back\repositories\Rates;

class LotteryWinsTask extends ImportTask
{
    use TaskWithFromToRequest;

    public function __construct(
        private readonly LotteryWins $lotteryWinsRepo,
        private readonly TaskSiteIdResolver $siteIdResolver,
        private readonly Rates $ratesRepo,
    ) {
    }

    protected function repository(): BaseRepository
    {
        return $this->lotteryWinsRepo;
    }

    protected function beforeFind(array &$row): bool
    {
        $row['site_id'] = $this->siteIdResolver->siteId();

        $row['rate_usd'] = $this->ratesRepo->getCrossRate($row['currency'], Rate::USD, $row['updated_at']);

        $row['rate_eur'] = $this->ratesRepo->getCrossRate($row['currency'], Rate::EUR, $row['updated_at']);

        /** todo: delete, compatibility with previous version in 1-2 days */
        $row['info'] = [
            'place' => $row['win_place'],
            'sum' => $row['win_amount'],
        ];

        return true;
    }
}
