<?php

namespace app\back\modules\task\actions\update;

use app\back\components\helpers\Arr;
use app\back\components\helpers\Json;
use app\back\entities\LyraStreamAtypicalDistributionStat;
use app\back\modules\checks\checks\rules\UserAtypicalDistributionInStreamRule;
use app\back\repositories\Checks;
use app\back\repositories\LyraStreamAtypicalDistributionStats;
use app\back\repositories\Refcodes;
use app\back\repositories\Users;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class LyraStreamEntityDistributionStatTask extends BaseLyraGeoTask
{
    public function process(): void
    {
        foreach ($this->getActiveRuleTypes() as $rule) {
            $typesInRule = Json::decode($rule['types']);
            $rows = $this->processDistributionStatByRule($typesInRule, (int)$rule['period'], (int) $rule['min_stream_size']);

            $types = Arr::leaveOnlyKeys(LyraStreamAtypicalDistributionStat::ALLOWED_DISTRIBUTION_TYPES, $typesInRule);

            $this->log->debug("Affected rows: {$rows} for distribution type: " . implode(' + ', $types));
            $this->affectedRows += $rows;
        }
    }

    private function processDistributionStatByRule(array $types, int $statPeriodDays, int $minStreamSize): int
    {
        $transaction = $this->db->beginTransaction();

        $this->db->createCommand()->delete(LyraStreamAtypicalDistributionStats::TABLE_NAME, ['AND',
            ['=', 'distribution_types', LyraStreamAtypicalDistributionStats::getDistributionTypesJoinField($types)],
            ['<', 'updated_at', new Expression("NOW()")],
        ])->execute();

        [$distributionFields, $joinTables] = LyraStreamAtypicalDistributionStats::getFieldsAndJoinsByDistributionTypes($types);

        $entityStatQuery = (new Query($this->db))
            ->select([
                'webmaster_id' => 'r.webmaster_id',
                'site_id' => 'u.site_id',
                'entity' => new Expression(LyraStreamAtypicalDistributionStats::getEntityJoinField($distributionFields)),
                'entity_count' => 'count(*)',
            ])
            ->from(['u' => Users::TABLE_NAME])
            ->innerJoin(['r' => Refcodes::TABLE_NAME], 'r.id = u.refcode_id');

        foreach ($joinTables as $tableJoinParams) {
            $entityStatQuery->innerJoin(...$tableJoinParams);
        }

        $entityStatQuery
            ->where(['>', 'u.date', new Expression("NOW() - INTERVAL '{$statPeriodDays}' DAY")])
            ->groupBy(['r.webmaster_id', 'u.site_id', 'entity']);


        $streamStatQuery = (new Query($this->db))
            ->select([
                'webmaster_id' => 'es.webmaster_id',
                'site_id' => 'es.site_id',
                'total_stream_count' => 'sum(es.entity_count)',
            ])
            ->from('es')
            ->groupBy(['webmaster_id', 'site_id']);

        $insert = (new Query($this->db))
            ->withQuery($entityStatQuery, 'es')
            ->withQuery($streamStatQuery, 'ss')
            ->select([
                'webmaster_id' => 'es.webmaster_id',
                'site_id' => 'es.site_id',
                'distribution_types' => new Expression("'" . LyraStreamAtypicalDistributionStats::getDistributionTypesJoinField($types) . "'"),
                'entity' => 'es.entity',
                'entities_count' => 'es.entity_count',
                'entity_stream_size' => 'ss.total_stream_count',
                'entity_stream_percent' => new Expression('round(es.entity_count / ss.total_stream_count * 100, 2)'),
                'updated_at' => new Expression('now()'),
            ])
            ->from('es')
            ->innerJoin('ss', 'ss.webmaster_id = es.webmaster_id and ss.site_id = es.site_id')
            ->where(['>=', 'ss.total_stream_count', $minStreamSize]);

        $affectedRows = $this->db->createCommand()
            ->insert(LyraStreamAtypicalDistributionStats::TABLE_NAME, $insert)
            ->execute();

        $transaction->commit();

        return $affectedRows;
    }

    private function getActiveRuleTypes(): array
    {
        $fromQuery = (new Query($this->db))
            ->select(['r' => new Expression("jsonb_array_elements(rules)")])
            ->from(['c' => Checks::TABLE_NAME])
            ->where(['active' => true]);

        return (new Query($this->db))
            ->select([
                'types' => new Expression("r->'distributionTypes'"),
                'period' => new Expression("MAX(r->>'period')"),
                'min_stream_size' => new Expression("MIN(r->>'streamSize')"),
            ])
            ->from(['r' => $fromQuery])
            ->where(['=', new Expression("r->>'id'"), (new \ReflectionClass(UserAtypicalDistributionInStreamRule::class))->getShortName()])
            ->groupBy(['types'])
            ->all();
    }
}
