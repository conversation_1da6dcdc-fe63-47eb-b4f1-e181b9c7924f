<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\update;

use app\back\components\helpers\Arr;
use app\back\components\helpers\DateHelper;
use app\back\components\MlHub;
use app\back\entities\Bin;
use app\back\entities\User;
use app\back\entities\UserGameToken;
use app\back\entities\UserHistory;
use app\back\entities\UserMetric;
use app\back\entities\UserTransaction;
use app\back\modules\task\BaseTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\Bins;
use app\back\repositories\UserGameTokens;
use app\back\repositories\UserMetrics;
use app\back\repositories\Users;
use app\back\repositories\UserSpecialInfos;
use app\back\repositories\UserTransactions;
use Psr\Log\LoggerInterface;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class UsersMetricVipProbabilityTask extends BaseTask
{
    private const int EXPIRED_AFTER_HOURS = 48;

    private const array COUNTRIES = ['TR', 'IT', 'RU', 'PL', 'DE'];
    private const string CARD_STATUSES_NONE = 'None';
    private const array CARD_STATUSES = [
        'ATM CARD', 'B2B', 'BUSINESS', 'BUSINESS ENHANCED', 'CLASSIC',
        'CLASSIC/GOLD', 'COMMERCIAL', 'COMMERCIAL AGRICULTURE', 'CORPORATE',
        'CORPORATE T&E', 'ELECTRON', 'GIFT', 'GOLD', 'INFINITE',
        'INFINITE BUSINESS', 'INFINITE/SIGNATURE', 'MIXED PRODUCT', self::CARD_STATUSES_NONE,
        'PERSONAL', 'PLATINUM', 'PLATINUM SALARY', 'PREFERRED BUSINESS',
        'PREMIUM', 'PREPAID', 'PREPAID BUSINESS', 'PREPAID CLASSIC',
        'PREPAID COMMERCIAL TRANSPORT', 'PREPAID ELECTRON',
        'PREPAID GOLD PAYROLL', 'PREPAID HEALTHCARE', 'PREPAID MEAL',
        'PREPAID PLATINUM', 'PREPAID PREFERRED BUSINESS',
        'PREPAID PURCHASING', 'PREPAID RELOADABLE', 'PREPAID TRAVEL MONEY',
        'PURCHASING', 'REWARDS', 'SIGNATURE', 'STANDARD', 'STANDARD SALARY',
        'SUPREME', 'TITANIUM', 'V PAY', 'VPAY', 'WORLD', 'WORLD BLACK',
        'WORLD BUSINESS', 'WORLD ELITE', 'WORLD FLEX', 'WORLD REWARDS',
    ];

    public function __construct(
        private readonly TaskSiteIdResolver $siteIdResolver,
        private readonly ConnectionInterface $db,
        private readonly UserMetrics $userMetricsRepo,
        private readonly Users $usersRepo,
        private readonly MlHub $mlHub,
    ) {
    }

    public function process(): void
    {
        $from = date(DateHelper::DATETIME_FORMAT_PHP, strtotime('-2 days', $this->fromTime));
        $to = date(DateHelper::DATETIME_FORMAT_PHP, strtotime('-2 days', strtotime($this->to)));

        if (strtotime($this->to) + self::EXPIRED_AFTER_HOURS * 3600 < time()) {
            throw new \RuntimeException("The metric has not actual. Expired time");
        }

        $query = (new Query($this->db));
        $query->withQuery(
            (new Query($this->db))
                ->select([
                    'u.user_id',
                    'u.site_id',
                    'u.registration_method',
                    'usi.dep_first_at',
                    'u.country',
                ])
                ->from(['u' => Users::TABLE_NAME])
                ->innerJoin(['usi' => UserSpecialInfos::TABLE_NAME], 'u.site_id = usi.site_id AND u.user_id = usi.user_id')
                ->where([
                    'AND',
                    ['>=', 'usi.dep_first_at', $from],
                    ['<', 'usi.dep_first_at', $to],
                    ['u.country' => self::COUNTRIES],
                    ['u.site_id' => $this->siteIdResolver->siteId()],
                ]),
            'target_users'
        );

        $query->withQuery(
            (new Query($this->db))
                ->select([
                    'tu.site_id',
                    'tu.user_id',
                    'us.amount_eur',
                    'us.is_first_success',
                    'b.status',
                    'hours_between_deps' => 'EXTRACT(EPOCH FROM (us.created_at - LAG(us.created_at) OVER (PARTITION BY tu.site_id, tu.user_id ORDER BY us.created_at))) / 3600'
                ])
                ->from(['us' => UserTransactions::TABLE_NAME])
                ->innerJoin(['tu' => 'target_users'], 'us.user_id = tu.user_id and us.site_id = tu.site_id')
                ->leftJoin(['b' => Bins::TABLE_NAME], 'b.bin = ' . Bin::binExpressionOfRequisite('us', 'wallet'))
                ->where(['AND',
                    ['us.op_id' => UserTransaction::OP_IN],
                    ['us.status' => UserTransaction::STATUS_SUCCESS],
                    ['~', 'us.wallet', '^\d{6}']
                ]),
            'deposit_intervals'
        );

        $query->withQuery(
            (new Query($this->db))
                ->select([
                    'di.site_id',
                    'di.user_id',
                    'dep_amount_eur' => 'SUM(di.amount_eur)',
                    'dep_count' => 'COUNT(*)',
                    'first_dep_sum_eur' => 'MAX(CASE WHEN di.is_first_success THEN di.amount_eur END)',
                    'avg_hours_between_deps' => 'AVG(di.hours_between_deps)',
                    'avg_dep_summ_eur' => '(SUM(di.amount_eur) / NULLIF(COUNT(*), 0))::decimal(10,2)',
                    'most_frequent_status_card' => 'MODE() WITHIN GROUP (ORDER BY di.status)',
                ])
                ->from(['di' => 'deposit_intervals'])
                ->groupBy(['di.site_id', 'di.user_id']),
            'deposit_stats'
        );

        $query->withQuery(
            (new Query($this->db))
                ->select([
                    'tu.site_id',
                    'tu.user_id',
                    'bet_sum_per_spin_eur' => 'AVG(ugt.bet_amount_eur / NULLIF(ugt.bet_count, 0))::decimal(10,2)',
                    'all_sessions_duration_sum' => 'SUM(EXTRACT(EPOCH FROM (ugt.last_action_at - ugt.created_at)) / 3600)',
                    'games_sessions_count' => 'COUNT(*)',
                ])
                ->from(['ugt' => UserGameTokens::TABLE_NAME])
                ->innerJoin(['tu' => 'target_users'], 'ugt.user_id = tu.user_id and ugt.site_id = tu.site_id')
                ->where(['AND',
                    ['ugt.balance_type' => UserGameToken::BALANCE_TYPE_REAL],
                    ['>', 'ugt.bet_amount_eur', 0]
                ])
                ->groupBy(['tu.site_id', 'tu.user_id']),
            'game_session_stats'
        );

        $cardStatuses = implode(',', array_map(fn ($status) => $this->db->quoteValue($status), self::CARD_STATUSES));
        $cardStatusNone = self::CARD_STATUSES_NONE;

        $query->select([
            'tu.site_id',
            'tu.user_id',
            'most_frequent_status_card' => "CASE WHEN most_frequent_status_card IN ($cardStatuses) THEN most_frequent_status_card ELSE '$cardStatusNone' END",
            'registration_method',
            'country',
            'dep_amount_eur' => 'COALESCE(dep_amount_eur, 0)',
            'dep_count' => 'COALESCE(dep_count, 0)',
            'first_dep_sum_eur' => 'COALESCE(first_dep_sum_eur, 0)',
            'bet_sum_per_spin_eur' => 'COALESCE(bet_sum_per_spin_eur, 0)',
            'all_sessions_duration_sum' => 'COALESCE(all_sessions_duration_sum, 0)',
            'avg_hours_between_deps' => 'COALESCE(avg_hours_between_deps, 0)',
            'games_sessions_count' => 'COALESCE(games_sessions_count, 0)',
            'avg_dep_summ_eur' => 'COALESCE(avg_dep_summ_eur, 0)',
        ])
            ->from(['tu' => 'target_users'])
            ->leftJoin(['ds' => 'deposit_stats'], 'tu.site_id = ds.site_id AND tu.user_id = ds.user_id')
            ->leftJoin(['gss' => 'game_session_stats'], 'tu.site_id = gss.site_id AND tu.user_id = gss.user_id');

        [, $metricColumn] = UserMetric::M_VIP_PROBABILITY_PER_MILLE;

        foreach ($query->batch(1000) as $rows) {
            $this->totalRows += count($rows);

            $vipProbabilities = $this->mlHub->inferVipProbabilities($rows);
            foreach ($vipProbabilities as $rowIndex => $row) {
                $value = round($row['vip_status_probability'] / UserMetric::PER_MILLE_MULTIPLIER);
                $siteUser = Arr::leaveOnlyKeys($rows[$rowIndex], ['site_id', 'user_id']);

                if ($value >= 600) { // >= 60%
                    $activeStatus = $value >= 800 ? User::ACTIVE_STATUS_ACTIVE : User::ACTIVE_STATUS_LOW; // >= 80%
                    $this->usersRepo->updateStatus($siteUser, UserHistory::SOURCE_AI, User::STATUS_PRE_VIP, $activeStatus);
                }

                //todo make batchUpsert
                $this->affectedRows += $this->userMetricsRepo->updateMetricByUser(
                    $siteUser['site_id'],
                    $siteUser['user_id'],
                    UserMetric::M_VIP_PROBABILITY_PER_MILLE,
                    $value,
                    updateConditions: ['<', UserMetrics::TABLE_NAME . ".$metricColumn", $value],
                );
            }
        }
    }
}
