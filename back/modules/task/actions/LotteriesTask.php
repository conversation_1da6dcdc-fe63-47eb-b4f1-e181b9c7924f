<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\modules\task\ImportTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\BaseRepository;
use app\back\repositories\Lotteries;

class LotteriesTask extends ImportTask
{
    use TaskWithFromToRequest;

    public function __construct(
        private readonly Lotteries $lotteriesRepo,
        private readonly TaskSiteIdResolver $siteIdResolver,
    ) {
    }

    protected function repository(): BaseRepository
    {
        return $this->lotteriesRepo;
    }

    protected function beforeFind(array &$row): bool
    {
        if (empty($row['name']) || empty($row['start_at']) || empty($row['finish_at'])) {
            return false;
        }

        $row['site_id'] = $this->siteIdResolver->siteId();

        return true;
    }
}
