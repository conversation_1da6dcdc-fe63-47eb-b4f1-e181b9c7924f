<?php

declare(strict_types=1);

namespace app\back\modules\api\clients\crm\info;

use app\back\components\validators\IntValidator;
use app\back\modules\api\components\Operators;
use app\back\repositories\Useragents;
use app\back\repositories\UserLogins;
use Yiisoft\Db\Query\Query;

class InfoLoginPlatformsMethod extends BaseCrmInfoMethod
{
    #[Operators(Operators::EQ)]
    #[IntValidator(1, 90)]
    protected array $period_days = [];

    public function columns(): array
    {
        return [
            'site_id' => 'l.site_id',
            'user_id' => 'l.user_id',
            'platform_id' => '(ARRAY_AGG(l.platform_id ORDER BY l.logins DESC))[1]',
        ];
    }

    public function getMainQuery(string $usersAlias): Query
    {
        $request = $this->createRequest();

        $countDays = $request->getParam('period_days', null, true);
        $from = date('Y-m-d', strtotime("-$countDays days"));

        $logins = (new Query($this->db))
            ->select([
                'ul.site_id',
                'ul.user_id',
                'ua.platform_id',
                'logins' => 'COUNT(*)',
            ])
            ->from(['ul' => UserLogins::TABLE_NAME])
            ->innerJoin($usersAlias, "$usersAlias.user_id = ul.user_id")
            ->leftJoin(['ua' => Useragents::TABLE_NAME], 'ua.id = ul.useragent_id')
            ->where(['>=', 'ul.date', $from])
            ->groupBy(['ul.site_id', 'ul.user_id', 'ua.platform_id']);

        return (new Query($this->db))
            ->from(['l' => $logins])
            ->groupBy(['l.site_id', 'l.user_id']);
    }

    public function getMainQueryAlias(): string
    {
        return 'l';
    }
}
