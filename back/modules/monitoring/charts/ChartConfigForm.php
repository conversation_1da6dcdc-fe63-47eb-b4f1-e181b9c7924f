<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts;

use app\back\components\Form;
use app\back\repositories\ChartPanels;

class ChartConfigForm
{
    use Form;
    use PanelRowColValidatorTrait;

    public function __construct(
        private readonly ChartPanels $chartPanelsRepo,
    ) {
    }

    public function getConfig(): array
    {
        return $this->panel->chart($this->row, $this->col)->getConfig();
    }
}
