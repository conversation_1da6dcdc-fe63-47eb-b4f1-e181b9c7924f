<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\panel;

use app\back\components\validators\CallableValidator;
use app\back\components\validators\IdValidator;
use app\back\entities\ChartPanel;

trait PanelIdValidatorTrait
{
    #[IdValidator]
    #[CallableValidator([self::class, 'loadPanel'])]
    public int $panelId;

    private ChartPanel $panel;

    public static function loadPanel(int $value, self $form): ?string
    {
        $form->panel = $form->chartPanelsRepo->findOneOr404(['id' => $value]);
        return null;
    }
}
