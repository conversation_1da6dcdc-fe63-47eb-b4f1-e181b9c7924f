<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\panel;

use app\back\components\Form;
use app\back\repositories\ChartPanels;

class PanelDeleteForm
{
    use Form;
    use PanelIdValidatorTrait;

    public function __construct(
        private readonly ChartPanels $chartPanelsRepo,
    ) {
    }

    public function delete(): void
    {
        $this->chartPanelsRepo->delete($this->panel);
    }
}
