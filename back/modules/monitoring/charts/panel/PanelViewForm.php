<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\panel;

use app\back\components\Form;
use app\back\modules\monitoring\charts\models\ChartPanelLayout;
use app\back\repositories\ChartPanels;

class PanelViewForm
{
    use Form;
    use PanelIdValidatorTrait;

    public function __construct(
        private readonly ChartPanels $chartPanelsRepo,
    ) {
    }

    public function response(): array
    {
        return [
            'name' => $this->panel->name,
            'layout' => (new ChartPanelLayout($this->panel->config))->getChartsLayoutConfig(),
        ];
    }
}
