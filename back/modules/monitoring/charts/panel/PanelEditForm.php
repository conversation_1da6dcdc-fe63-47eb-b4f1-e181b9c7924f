<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\panel;

use app\back\components\BaseAuthAccess;
use app\back\components\FormGrid;
use app\back\components\helpers\Arr;
use app\back\components\Permission;
use app\back\modules\monitoring\charts\models\ChartPanelLayout;
use app\back\repositories\ChartPanels;

class PanelEditForm
{
    use FormGrid;
    use PanelIdValidatorTrait;

    public function __construct(
        public readonly ChartPanels $chartPanelsRepo,
        private readonly BaseAuthAccess $auth,
        private readonly PanelSaveForm $panelSaveForm,
    ) {
    }

    protected function blocks(): array
    {
        return [
            [
                $this->textInputCell(8, 'name', 'Name'),
                $this->textInputCell(2, 'cols', 'Cols'),
                $this->textInputCell(2, 'rows', 'Rows'),
            ],
        ];
    }

    public function complexResponse(): array
    {
        $form = $this->panelSaveForm;
        Arr::configure($form, [
            'panelId' => $this->panel->id,
            'name' => $this->panel->name,
            'cols' => (int)$this->panel->config['cols'],
            'rows' => (int)$this->panel->config['rows']
        ]);

        return [
            'id' => $this->panel->id,
            'form' => $form->response(),
            'layout' => (new ChartPanelLayout($this->panel->config))->getChartsLayout(),
            'showSqlButton' => $this->auth->can(Permission::PERM_VIEW_SQL),
        ];
    }
}
