<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\models;

class TypeCastConfigureHelper
{
    public static function configure(object $object, array $properties): void
    {
        $class = new \ReflectionClass($object::class);
        foreach ($properties as $name => $value) {
            if (!property_exists($object, $name)) {
                continue;
            }
            $propertyType = $class->getProperty($name)->getType()?->getName();
            if ($propertyType === 'bool' && is_int($value)) {
                $object->$name = (bool)$value;
            } elseif ($propertyType === 'int' && is_string($value)) {
                $object->$name = (int)$value;
            } else {
                $object->$name = $value;
            }
        }
    }
}
