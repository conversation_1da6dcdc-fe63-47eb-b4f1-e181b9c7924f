<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\models;

use app\back\components\Container;
use app\back\components\exceptions\ExpectationFailedException;
use app\back\components\FormGrid;
use app\back\components\helpers\Arr;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\SafeValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\components\validators\StringValidator;
use app\back\modules\monitoring\charts\sources\BaseChartSource;
use app\back\modules\monitoring\charts\sources\BettingChartSource;
use app\back\modules\monitoring\charts\sources\LoginsChartSource;
use app\back\modules\monitoring\charts\sources\S2pOrdersChartSource;
use app\back\modules\monitoring\charts\sources\UsersChartSource;
use app\back\modules\monitoring\charts\sources\UsersGamesChartSource;
use app\back\modules\monitoring\charts\sources\UsersStatChartSource;
use app\back\modules\monitoring\charts\sources\YhOnlineChartSource;

class Serie
{
    use FormGrid;

    private const int NULLS_STRATEGY_TO_ZERO = 1;
    private const int NULLS_STRATEGY_SKIP = 2;
    private const int NULLS_STRATEGY_SKIP_CONNECT = 3;

    public const array NULLS_STRATEGIES = [
        self::NULLS_STRATEGY_TO_ZERO => 'Empty to zero',
        self::NULLS_STRATEGY_SKIP => 'Skip empty',
        self::NULLS_STRATEGY_SKIP_CONNECT => 'Skip and connect empty spaces',
    ];

    // String values stored in DB
    // Don't change!!!
    public const array SOURCES = [
        'users' => UsersChartSource::class,
        'stats_count' => UsersStatChartSource::class,
        's2p_count' => S2pOrdersChartSource::class,
        'logins' => LoginsChartSource::class,
        'users_games' => UsersGamesChartSource::class,
        'yh_online' => YhOnlineChartSource::class,
        'betting' => BettingChartSource::class,
    ];

    public const string CSS_COLORS_PREFIX = 'highcharts-color-';
    /** @link /front/css/_colors.scss */
    public const array COLORS = [
        'fu-maroon',
        'fu-pomegranate',
        'fu-alizarin',
        'fu-pumpkin',
        'fu-tomato',
        'fu-coral',
        'fu-salmon',
        'fu-chocolate',
        'fu-carrot',
        'fu-orange',
        'fu-sun-flower',
        'fu-gold',
        'fu-wheat',
        'fu-dark-khaki',
        'fu-pale-green',
        'fu-emerald',
        'fu-nephritis',
        'fu-turquoise',
        'fu-green-sea',
        'fu-teal',
        'fu-sky-blue',
        'fu-peter-river',
        'fu-belize-hole',
        'fu-purple',
        'fu-wisteria',
        'fu-amethyst',
        'fu-plum',
    ];

    #[StringValidator(1, 100)]
    public string $name = 'New serie';
    #[StringInArrayValidator(self::COLORS, true)]
    public string $className = 'fu-sky-blue';
    #[IntValidator]
    public int $order = 0;
    #[StringInArrayValidator(self::SOURCES)]
    public string $source;
    #[SafeValidator]
    public array $sourceConfig = [];
    #[IntInArrayValidator(self::NULLS_STRATEGIES)]
    public int $nulls_strategy = self::NULLS_STRATEGY_TO_ZERO;

    public static function fromConfig(array $config): self
    {
        $serie = new self();

        TypeCastConfigureHelper::configure($serie, $config);

        return $serie;
    }

    protected function blocks(): array
    {
        /** @var BaseChartSource $source */
        $source = (static::SOURCES[$this->source]);

        return [
            [
                $this->textInputCell(5, 'name', 'Name'),
                $this->textStaticCell(3, $source::title(), 'Source'),
                $this->selectCell(2, 'nulls_strategy', 'Nulls strategy', [
                    'multiple' => false,
                    'list' => Arr::assocToIdName(self::NULLS_STRATEGIES),
                ]),
                $this->textInputCell(2, 'order', 'Order'),
            ],
            [
                $this->colorListCell(12, 'className', 'Color', [
                    'list' => Arr::columnToIdName(self::COLORS),
                    'multiple' => false,
                ]),
            ]
        ];
    }

    public function getData(Container $container, string $fromDate, string $toDate, int $divider): array
    {
        return $this->loadAndValidateChartSource($container, $fromDate, $toDate, $divider)
            ->data();
    }

    public function getSql(Container $container, string $fromDate, string $toDate, int $divider): string
    {
        return $this->loadAndValidateChartSource($container, $fromDate, $toDate, $divider)
            ->queryString();
    }

    private function loadAndValidateChartSource(Container $container, string $fromDate, string $toDate, int $divider): BaseChartSource
    {
        $config = $this->getChartSource($container, [
            'fillNullsToZero' => $this->nulls_strategy === self::NULLS_STRATEGY_TO_ZERO,
        ]);

        $config->load(array_merge($this->sourceConfig, [
            'from' => $fromDate,
            'to' => $toDate,
            'divider' => $divider,
        ]));

        if (!$config->validate($config->getRequest()->attrsByFilterName($config::DATE_COLUMN))) {
            throw new ExpectationFailedException(current($config->getFirstErrors()));
        }

        return $config;
    }

    public function getHighchartsConfig(Chart $chart): array
    {
        return [
            'name' => $this->name,
            'order' => $this->order,
            'className' => self::CSS_COLORS_PREFIX . $this->className,
            'connectNulls' => $this->nulls_strategy === static::NULLS_STRATEGY_SKIP_CONNECT,
            'type' => $chart->type,
            'stacking' => match ($chart->stacking) {
                Chart::STACKING_NORMAL => 'normal',
                Chart::STACKING_PERCENT => 'percent',
                default => null,
            },
            'marker' => [
                'enabled' => $chart->type === Chart::TYPE_SCATTER ? true : null,
            ],
            'dataGrouping' => [
                'enabled' => false,
            ],
            'data' => [],
        ];
    }

    public function getChartSource(Container $container, array $config = []): BaseChartSource
    {
        /** @see BaseChartSource::__construct() */
        $source = new (static::SOURCES[$this->source])($container);
        Arr::configure($source, $config);
        return $source;
    }

    public function getAttributes(): array
    {
        return Arr::leaveOnlyKeys(get_object_vars($this), ['name', 'className', 'order', 'source', 'sourceConfig', 'nulls_strategy']);
    }
}
