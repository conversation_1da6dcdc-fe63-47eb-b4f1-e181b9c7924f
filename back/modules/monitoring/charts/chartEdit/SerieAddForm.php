<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\chartEdit;

use app\back\components\Container;
use app\back\components\FormGrid;
use app\back\components\helpers\Arr;
use app\back\components\validators\StringInArrayValidator;
use app\back\modules\monitoring\charts\models\Serie;
use app\back\modules\monitoring\charts\PanelRowColValidatorTrait;
use app\back\modules\monitoring\charts\sources\BaseChartSource;
use app\back\repositories\ChartPanels;

class SerieAddForm
{
    use FormGrid;
    use PanelRowColValidatorTrait;

    #[StringInArrayValidator(Serie::SOURCES)]
    public string $source;

    public function __construct(
        private readonly ChartPanels $chartPanelsRepo,
    ) {
    }

    protected function blocks(): array
    {
        return [
            [
                $this->selectCell(3, 'source', 'Source', [
                    'multiple' => false,
                    'list' => Arr::assocToIdName(self::getSourcesNames()),
                ]),
                $this->submitCell(3, 'Add', [
                    'buttonIcon' => 'icn-plus',
                    'buttonStyle' => 'btn-success',
                ]),
            ],
        ];
    }

    private static function getSourcesNames(): array
    {
        $result = [];
        /** @var  BaseChartSource $sourceClass  */
        foreach (Serie::SOURCES as $sourceKey => $sourceClass) {
            $result[$sourceKey] = $sourceClass::title();
        }

        return $result;
    }

    public function add(Container $container): void
    {
        $serie = new Serie();
        $serie->source = $this->source;

        $chartSource = $serie->getChartSource($container);
        $chartSource->loadDefaultAttributes();
        $serie->sourceConfig = $chartSource->getAttributes();

        $this->panel->config = $this->panel->updatedChartSerieConfig($this->row, $this->col, null, $serie);
        $this->chartPanelsRepo->update($this->panel, ['config']);
    }
}
