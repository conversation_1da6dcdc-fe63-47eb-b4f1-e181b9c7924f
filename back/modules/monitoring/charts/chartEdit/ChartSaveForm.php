<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts\chartEdit;

use app\back\components\Form;
use app\back\components\validators\CallableValidator;
use app\back\modules\monitoring\charts\models\Chart;
use app\back\modules\monitoring\charts\PanelRowColValidatorTrait;
use app\back\repositories\ChartPanels;

class ChartSaveForm
{
    use Form;
    use PanelRowColValidatorTrait;

    #[CallableValidator([self::class, 'validateConfig'])]
    public array $config = [];

    public function __construct(
        private readonly ChartPanels $chartPanelsRepo,
    ) {
    }

    public static function validateConfig(mixed $value): ?string
    {
        $chart = new Chart();
        $chart->validateOrException($value);

        return null;
    }

    public function save(): void
    {
        $this->panel->config = $this->panel->updatedConfigChart($this->row, $this->col, $this->config);
        $this->chartPanelsRepo->update($this->panel, ['config']);
    }
}
