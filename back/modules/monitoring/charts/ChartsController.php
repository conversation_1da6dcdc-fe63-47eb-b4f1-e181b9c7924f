<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts;

use app\back\components\accessCheck\AccessCheckPage;
use app\back\components\accessCheck\AccessCheckSecureDictRequest;
use app\back\components\Container;
use app\back\components\Request;
use app\back\components\WebController;
use app\back\repositories\ChartPanels;

#[AccessCheckPage]
#[AccessCheckSecureDictRequest(ChartPanels::class, 'JSON.panelId')]
class ChartsController extends WebController
{
    public const string RESOURCE_NAME = '/monitoring/charts';

    public function actionChartConfig(ChartConfigForm $form, Request $request): array
    {
        $form->validateOrException($request->json());

        return $form->getConfig();
    }

    public function actionChartData(Container $container, ChartDataForm $form, Request $request): array
    {
        $form->validateOrException($request->json());
        return $form->data($container);
    }
}
