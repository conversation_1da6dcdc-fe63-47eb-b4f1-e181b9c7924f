<?php

declare(strict_types=1);

namespace app\back\modules\checks\checks\rules;

use app\back\components\helpers\Arr;
use app\back\components\InBuilder;
use app\back\components\SiteUserBuilder;
use app\back\components\validators\CallableValidator;
use app\back\components\validators\IntArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\components\validators\StringMultilineValidator;
use app\back\entities\LyraStreamAtypicalDistributionStat;
use app\back\repositories\LyraStreamAtypicalDistributionStats;
use app\back\repositories\Refcodes;
use app\back\repositories\Users;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class UserAtypicalDistributionInStreamRule extends BaseSiteUserRule
{
    #[IntArrayValidator(LyraStreamAtypicalDistributionStat::ALLOWED_DISTRIBUTION_TYPES, allowEmpty: false)]
    public array $distributionTypes;
    #[StringMultilineValidator(1)]
    #[CallableValidator([self::class, 'validateRulesMap'])]
    public ?string $rulesMap = null;
    #[IntValidator(1)]
    public ?int $period = null;
    #[IntValidator]
    public int $streamSize = 0;
    #[StringInArrayValidator(self::OPERATORS)]
    public string $streamSizeOperator = self::OPERATOR_GREATER_OR_EQ;

    public ?array $rulesMapArray = null;

    protected function getQuery(ConnectionInterface $db, InBuilder $inBuilder): Query
    {
        $inBuilder->setMap(['intval', 'intval']);

        $distributionTableNameAlias = "lsads";

        $cond = [];
        $expParams = [];
        $i = 0;
        foreach ($this->rulesMapToArray() as $steamUsersCountSize => $positiveCasePercent) {
            $streamSizeKey = "stream_size_exp_{$i}";
            $streamCaseKey = "stream_case_exp_{$i}";
            $cond[] = "{$distributionTableNameAlias}.entity_stream_size >= :$streamSizeKey THEN {$distributionTableNameAlias}.entity_stream_percent >= :$streamCaseKey";
            $expParams[$streamSizeKey] = $steamUsersCountSize;
            $expParams[$streamCaseKey] = $positiveCasePercent;
            $i++;
        }

        $q = (new Query($db))
            ->select([
                static::RESULT_COL_RESULT => new Expression("CASE WHEN " . implode(' WHEN ', $cond) . ' ELSE FALSE END', $expParams),
                static::RESULT_COL_PK => SiteUserBuilder::siteUserQueryExpression('f'),
                static::RESULT_COL_INFO => new Expression("array_to_string({$distributionTableNameAlias}.entity, ',') || ' ' || {$distributionTableNameAlias}.entity_stream_percent || '%'")
            ])
            ->from($inBuilder->table('f', ['site_id', 'user_id']))
            ->innerJoin(['u' => Users::TABLE_NAME], 'u.site_id = f.site_id and u.user_id = f.user_id')
            ->leftJoin(['r' => Refcodes::TABLE_NAME], 'r.id = u.refcode_id and r.webmaster_id is not null');

        return $this->decorateQueryByDistributionType($q, $distributionTableNameAlias);
    }

    private function decorateQueryByDistributionType(Query $query, string $distributionTableNameAlias = 'lsads'): Query
    {

        [$fieldsInDistribution, $additionalTables] = LyraStreamAtypicalDistributionStats::getFieldsAndJoinsByDistributionTypes($this->distributionTypes);

        foreach ($additionalTables as $additionalTableAlias) {
            $query->leftJoin(...$additionalTableAlias);
        }

        $query->leftJoin(
            [$distributionTableNameAlias => LyraStreamAtypicalDistributionStats::TABLE_NAME],
            "r.webmaster_id = {$distributionTableNameAlias}.webmaster_id and u.site_id = {$distributionTableNameAlias}.site_id and {$distributionTableNameAlias}.entity = " . LyraStreamAtypicalDistributionStats::getEntityJoinField($fieldsInDistribution) . " and {$distributionTableNameAlias}.distribution_types = :dt",
            ['dt' => LyraStreamAtypicalDistributionStats::getDistributionTypesJoinField($this->distributionTypes)]
        );

        return $query->where([$this->streamSizeOperator, 'lsads.entity_stream_size', $this->streamSize]);
    }

    public function getParamsSignatureParts(): array
    {
        return [
            'distributionTypes',
            'rulesMap',
            'period',
            'streamSize',
            'streamSizeOperator',
        ];
    }

    public function getMessage(): string
    {
        $parts = [];
        foreach ($this->rulesMapToArray() as $usersCount => $positiveCasePercent) {
            $parts[] = "Atypical devices in stream with {$usersCount} users >= $positiveCasePercent";
        }
        return implode(', ', $parts);
    }

    private function rulesMapToArray(): array
    {
        if (!is_null($this->rulesMapArray)) {
            return $this->rulesMapArray;
        }

        $this->rulesMapArray = [];
        foreach (explode("\n", $this->rulesMap) as $line) {
            $matches = [];
            if (!preg_match('/^([0-9]+)\s([0-9]+)%$/i', $line, $matches)) {
                throw new \InvalidArgumentException('Not valid value!');
            }
            [, $usersCount, $positiveCasePercent] = $matches;
            $this->rulesMapArray[$usersCount] = (float)$positiveCasePercent;
        }

        return $this->rulesMapArray;
    }


    public function getLabel(): string
    {
        return "<span class=\"badge bg-secondary\"'>{$this->getMessage()}</span>";
    }

    protected function blocks(): array
    {
        return [
            [
                $this->selectCell(3, 'distributionTypes', 'Entity types', [
                    'list' => Arr::assocToIdName(LyraStreamAtypicalDistributionStat::ALLOWED_DISTRIBUTION_TYPES),
                    'multiple' => true,
                ]),
                $this->textAreaCell(3, 'rulesMap', 'Positive check rules', [
                    'hint' => 'Rule map for each line. Example: 5 60% means 5 users and greater have min 60% level of atypical devices'
                ]),
                $this->textInputCell(3, 'streamSize', 'Stream size', [
                    'operators' => Arr::assocToIdName([
                        self::OPERATOR_GREATER_OR_EQ => self::OPERATOR_GREATER_OR_EQ,
                        self::OPERATOR_LOWER_OR_EQ => self::OPERATOR_LOWER_OR_EQ,
                        self::OPERATOR_GREATER => self::OPERATOR_GREATER,
                        self::OPERATOR_LOWER => self::OPERATOR_LOWER,
                    ]),
                    'operatorPostfix' => 'Operator',
                ]),
                $this->textInputCell(3, 'period', 'Period (days)'),
            ]
        ];
    }

    public static function validateRulesMap(string $value, self $form, array $context): ?string
    {
        try {
            $form->rulesMap = $value;
            $form->rulesMapToArray();
            return null;
        } catch (\InvalidArgumentException $e) {
            return $e->getMessage();
        }
    }
}
