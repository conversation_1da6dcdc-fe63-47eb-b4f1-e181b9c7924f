<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Lotteries;

use app\back\components\helpers\DateHelper;
use app\back\components\helpers\Json;
use app\back\entities\LotteryWin;
use app\back\modules\reports\columns\BooleanColumn;
use app\back\modules\reports\columns\CountColumn;
use app\back\modules\reports\columns\DateColumn;
use app\back\modules\reports\columns\IsCompletedColumn;
use app\back\modules\reports\columns\LotteryTicketIdColumn;
use app\back\modules\reports\columns\SimpleColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\SiteUserColumn;
use app\back\modules\reports\columns\UserIdColumn;
use app\back\modules\reports\components\BaseReportConfig;
use app\back\repositories\Lotteries;
use app\back\repositories\LotteryTickets;
use app\back\repositories\LotteryWins;

class LotteriesConfig extends BaseReportConfig
{
    public function rules(): array
    {
        return [
            [['start_at', 'site_id'], 'required']
        ];
    }

    public function defaultParams(): array
    {
        return [
            ['start_at', DateHelper::monthBegin(), '>='],
            ['start_at', DateHelper::yesterday(), '<='],
            ['site_id', []],
            ['is_prizes_distributed', 1],
            ['columns', ['site_id', 'site_user', 'name', 'ticket_id', 'win_sum']],
            ['metrics', ['win_sum']],
        ];
    }

    public function filters(): array
    {
        return [
            'Lottery' => [
                'start_at' => [DateColumn::class, ['l' => 'start_at'], 'title' => 'Started at'],
                'lottery_id' => [LotteryIdColumn::class, 'l'],
                'is_prizes_distributed' => [BooleanColumn::class, ['l' => 'is_prizes_distributed'], 'title' => 'Is prizes distributed'],
                'is_finished' => [IsCompletedColumn::class, ['l' => 'finish_at']],
            ],
            'User' => [
                'site_id' => [SiteIdColumn::class, 'l'],
                'site_user' => [SiteUserColumn::class, 'lt'],
            ],
            'Win' => [
                'place' => [LotteryWinPlaceColumn::class, 'lw'],
            ],
        ];
    }

    public function columns(): array
    {
        return [
            'Lottery' => [
                'lottery_id' => [LotteryIdColumn::class, 'l'],
                'name' => [SimpleColumn::class, ['l' => 'name'], 'title' => 'Name'],
                'start_at' => [DateColumn::class, ['l' => 'start_at'], 'title' => 'Started at'],
                'finish_at' => [DateColumn::class, ['l' => 'finish_at'], 'title' => 'Finished at'],
                'is_finished' => [IsCompletedColumn::class, ['l' => 'finish_at']],
                'is_prizes_distributed' => [BooleanColumn::class, ['l' => 'is_prizes_distributed'], 'title' => 'Is prizes distributed'],
            ],
            'User' => [
                'site_id' => [SiteIdColumn::class, 'l'],
                'site_user' => [SiteUserColumn::class, 'lt'],
                'user_id' => [UserIdColumn::class, 'lt'],
            ],
            'Ticket' => [
                'ticket_id' => [LotteryTicketIdColumn::class, 'lt'],
                'is_gold' => [LotteryTicketIsGoldColumn::class, 'lt'],
            ],
            'Win' => [
                'place' => [LotteryWinPlaceColumn::class, 'lw'],
                'win_sum' => [CountColumn::class, ['expr' => Json::sqlStrGet('lw.info', LotteryWin::INFO_COLUMN_SUM), 'lw'], 'title' => 'Win sum'],
            ],
        ];
    }

    public function metrics(): array
    {
        return [
            'Main' => [
                'win_sum' => [CountColumn::class, ['expr' => 'SUM(' . Json::sqlStrGet('lw.info', LotteryWin::INFO_COLUMN_SUM) . '::float)', 'lw'], 'title' => 'Win sum'],
            ]
        ];
    }

    public function groups(): array
    {
        return [
            'Lottery' => [
                'lottery_id' => [LotteryIdColumn::class, 'l'],
                'name' => [SimpleColumn::class, ['l' => 'name'], 'title' => 'Name'],
                'is_finished' => [IsCompletedColumn::class, ['l' => 'finish_at']],
            ],
            'User' => [
                'site_id' => [SiteIdColumn::class, 'l'],
                'site_user' => [SiteUserColumn::class, 'lt'],
            ],
            'Ticket' => [
                'is_gold' => [LotteryTicketIsGoldColumn::class, 'lt'],
            ],
            'Win' => [
                'place' => [LotteryWinPlaceColumn::class, 'lw'],
            ],
        ];
    }

    public function tableMap(): array
    {
        return [
            'l' => [Lotteries::TABLE_NAME],
            'lt' => [LotteryTickets::TABLE_NAME, 'l.site_id = lt.site_id AND l.lottery_id = lt.lottery_id'],
            'lw' => [LotteryWins::TABLE_NAME, 'lw.site_id = lt.site_id AND lw.ticket_id = lt.ticket_id AND lw.lottery_id = lt.lottery_id', ['lt']],
        ];
    }
}
