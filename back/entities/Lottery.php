<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\BooleanValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\StringValidator;

class Lottery extends BaseEntity
{
    #[IdValidator]
    public int $site_id;
    #[IdValidator]
    public int $lottery_id;
    #[StringValidator(0, 100)]
    public string $name;
    #[DateTimeImmutableValidator]
    public \DateTimeImmutable $start_at;
    #[DateTimeImmutableValidator]
    public \DateTimeImmutable $finish_at;
    #[BooleanValidator]
    public ?bool $is_prizes_distributed;
}
