<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\BooleanValidator;
use app\back\components\validators\CurrencyFormatValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\JsonObjectValidator;
use app\back\components\validators\MoneyValidator;

class LotteryWin extends BaseEntity
{
    public const string INFO_COLUMN_SUM = 'sum';
    public const string INFO_COLUMN_PLACE = 'place';
    public const string INFO_COLUMN_USER_NAME = 'user_name';

    #[IdValidator]
    public int $site_id;
    #[IdValidator]
    public int $win_id;
    #[IdValidator]
    public int $lottery_id;
    #[IdValidator]
    public int $ticket_id;
    #[IdValidator]
    public int $prize_id;
    #[BooleanValidator]
    public ?bool $is_distributed;
    #[JsonObjectValidator]
    public array $info = [];  // todo: delete
    #[DateTimeImmutableValidator]
    public \DateTimeImmutable $updated_at;
    #[IntValidator]
    public ?int $win_place;
    #[MoneyValidator(2, PHP_INT_MIN)]
    public ?string $win_amount;
    #[CurrencyFormatValidator]
    public ?string $currency;
    #[MoneyValidator(10)]
    public ?string $rate_usd;
    #[MoneyValidator(10)]
    public ?string $rate_eur;
}
