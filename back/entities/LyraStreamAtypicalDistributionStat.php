<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\FloatValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntArrayValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\StringArrayValidator;
use app\back\components\validators\StringValidator;

class LyraStreamAtypicalDistributionStat extends BaseEntity
{
    public const int ENTITY_TYPE_UA_DEVICE_DISTRIBUTION = 1;
    public const int ENTITY_TYPE_REG_CITY_DISTRIBUTION = 2;
    public const int ENTITY_TYPE_WEBGL_RENDERER_DISTRIBUTION = 3;

    public const array ALLOWED_DISTRIBUTION_TYPES = [
        self::ENTITY_TYPE_UA_DEVICE_DISTRIBUTION => 'UA device',
        self::ENTITY_TYPE_REG_CITY_DISTRIBUTION => 'Registration city',
        self::ENTITY_TYPE_WEBGL_RENDERER_DISTRIBUTION => 'WebGL Renderer',
    ];
    #[StringValidator]
    public string $webmaster_id;
    #[IdValidator]
    public int $site_id;
    #[IntArrayValidator(self::ALLOWED_DISTRIBUTION_TYPES)]
    public array $distribution_types;

    #[StringArrayValidator(1, 100)]
    public array $entity;
    #[IntValidator]
    public int $entities_count;
    #[IntValidator]
    public int $entity_stream_size;
    #[FloatValidator(0, 100)]
    public string $entity_stream_percent;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $updated_at;
}
