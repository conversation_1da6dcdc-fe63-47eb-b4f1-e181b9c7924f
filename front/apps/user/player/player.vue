<template>
    <div>
        <div class="container">
            <Breadcrumbs />
            <SearchForm
                @search="onSearch"
            />
        </div>

        <div class="container-fluid">
            <TabsRouter
                :tabs="allTabs"
                tabIdRouteParamName="tabId"
                emptyTabId=""
                :showClose="$route.params.tabId !== ''"
                class="mt-3"
                @change="onTabChange"
                @close="onTabClose"
            >
                <template #default="{tabId}">
                    <SearchResult
                        v-show="tabId === ''"
                        :searchPromise="searchPromise"
                    />
                    <One
                        v-if="tabId?.match(/\d+-\d+/)"
                        :siteUser="tabId"

                        @updateUserTabTitle="onUpdateUserTabTitle"
                    />
                </template>
                <template #afterTabs="{}">
                    <li class="nav-item ms-auto">
                        <a
                            href="javascript:void(0);"
                            class="nav-link"
                            title="Close all tabs"
                            @click.prevent="onTabCloseAll"
                        ><Icona name="icn-power" /></a>
                    </li>
                </template>
            </TabsRouter>
        </div>
    </div>
</template>

<script lang="ts">
import { Icona, TabsRouter } from '@/components'
import { Breadcrumbs } from '@/widgets'
import { defineComponent } from 'vue'
import { TableType, Tab } from '@/types'
import One from './one.vue'
import SearchForm from './search-form.vue'
import SearchResult from './search-result.vue'
import { RouteLocationNormalized } from 'vue-router'
import { useTitleBreadcrumbs } from '@/utils/title-breadcrumbs.ts'

const searchRoute = { name: 'player', params: { tabId: '' } }

export default defineComponent({
    components: {
        Icona,
        SearchResult,
        One,
        SearchForm,
        TabsRouter,
        Breadcrumbs,
    },
    beforeRouteUpdate (to: RouteLocationNormalized, from: RouteLocationNormalized) {
        if (
            to.params.tabId !== from.params.tabId &&
            to.params.subTabId !== from.params.subTabId &&
            from.params.tabId !== '' &&
            from.params.subTabId !== ''
        ) {
            // Mutate new route to show same subTabId with same params
            to.params.subTabId = from.params.subTabId
            to.query = from.query
            this.$router.replace({
                name: 'player',
                params: {
                    tabId: to.params.tabId,
                    subTabId: to.params.subTabId,
                },
                query: to.query,
            })
        }
    },
    setup() {
        return {
            titleBreadcrumbs: useTitleBreadcrumbs(),
        }
    },
    data () {
        return {
            tabs: [] as Tab[],
            searchPromise: null as Promise<TableType> | null,
        }
    },
    computed: {
        allTabs (): Tab[] {
            const result = this.tabs.slice()
            result.unshift({
                title: 'Search results',
                route: searchRoute,
            })

            return result
        },
    },
    async created () {
        this.restoreFromStorage()
    },
    methods: {
        async onSearch (searchPromise: Promise<TableType> | null) {
            this.searchPromise = searchPromise
        },

        onUpdateUserTabTitle (siteUser: string, title: string, blockName?: string) {
            const userTabIndex = this.getTabIndex(siteUser)

            if (userTabIndex === -1) {
                return // TODO: handle error
            }

            this.tabs[userTabIndex].title = title;
            this.titleBreadcrumbs.setTitle(title, true)
            if (blockName) {
                this.titleBreadcrumbs.setTitle(blockName, true)
            }
            this.titleBreadcrumbs.setDocumentTitle(`${title} - ${blockName}`)
            this.saveToStorage() // Save only after updated tab title
        },

        onTabChange (tabId: string| undefined) {
            if (tabId === '' || tabId === undefined) {
                return
            }

            const userTabIndex = this.getTabIndex(tabId)
            if (userTabIndex === -1) {
                this.tabs.push({
                    route: { name: 'player', params: { tabId } },
                    title: tabId,
                })
                this.saveToStorage()
            }
        },
        onTabClose (tabId: string) {
            if (tabId === '') {
                return
            }

            const tabIndex = this.getTabIndex(tabId)
            if (tabIndex < this.tabs.length && tabIndex >= 0) {
                this.tabs.splice(tabIndex, 1)
                this.saveToStorage()
                this.$router.push(this.allTabs[tabIndex].route!)
            }
        },

        onTabCloseAll () {
            this.tabs = []
            this.saveToStorage()
            this.$router.push(this.allTabs[0].route!)
        },

        getTabIndex (tabId: string) {
            return this.tabs.findIndex((tab: Tab) => tab.route?.params?.tabId === tabId)
        },

        restoreFromStorage () {
            const tabs: string | null = localStorage.getItem('players-tabs')

            if (!tabs) {
                return
            }

            const data: Tab[] | null | undefined = JSON.parse(tabs)

            if (data === undefined || data === null) {
                return
            }

            this.tabs = data.map((tab: Tab) => {
                return {
                    title: tab.title,
                    route: {
                        name: 'player',
                        params: {
                            tabId: tab.route!.params!.tabId,
                        },
                    },
                }
            })
        },

        saveToStorage () {
            localStorage.setItem('players-tabs', JSON.stringify(this.tabs))
        },
    },
})
</script>
