<template>
    <div v-if="panel">
        <div class="row">
            <div class="col-sm-10">
                <FormGrid
                    v-bind="panel.form"
                    @change="onPanelChange"
                />
            </div>
            <div class="col-sm-2">
                <label>&nbsp;</label><br>
                <button
                    type="button"
                    class="btn btn-danger"
                    @click="onPanelDelete"
                >
                    <Icona name="icn-delete" /> Delete
                </button>
            </div>
        </div>

        <table class="charts-panel table-fixed">
            <tr v-for="(cols, row) in panel.layout" :key="row">
                <td
                    v-for="(chart, col) in cols"
                    :key="col"
                    class="align-top"
                    :rowspan="Math.max(chart.row_span, 1)"
                    :colspan="Math.max(chart.col_span, 1)"
                >
                    <div
                        class="btn btn-lg d-block"
                        :draggable="!inProcess"
                        :class="getChartSelectorClasses(row, col, chart)"
                        @click.prevent="onChartSelect(row, col)"
                        @dragstart="onDragStart($event, row, col, chart)"
                        @dragover.prevent
                        @drop.prevent="onDrop(row, col, chart)"
                        @dragend.prevent="onDragEnd"
                    >
                        {{ chart.name || '(empty)' }}
                    </div>
                </td>
            </tr>
        </table>
        <div v-if="selectedChart">
            <template v-if="panel.layout[selectedChart.row][selectedChart.col].name === null">
                <FormGrid
                    v-bind="chartAddForm"
                    @change="chartAddForm.values = $event"
                    @submit="onChartAdd"
                />
            </template>
            <template v-else>
                <EditChart
                    :panelRowCol="{ panelId, ...selectedChart }"
                    :inProcess="inProcess"
                    :showSqlButton="panel.showSqlButton"
                    @chartDelete="onChartDelete"
                    @panelUpdate="loadPanel"
                />
            </template>
        </div>
        <p
            v-if="!selectedChart"
            class="font-italic text-center small"
        >
            Select chart to edit or drag to rearrange
        </p>
    </div>
</template>

<script lang="ts">

import { FormGrid, HighchartsLoader, Icona } from '@/components'
import { defineComponent } from 'vue'
import { useTitleBreadcrumbs } from '@/utils/title-breadcrumbs.ts'
import { FormGridType, FormValue, Values, NextWithReload } from '@/types'
import EditChart from './edit-chart.vue'

interface RouteParams {
    panelId?: string
    rowCol?: string
    serieIndex?: string
}

interface RowCol {
    row: number
    col: number
}

export interface PanelIdRowCol extends RowCol {
    panelId: string
}

interface Chart {
    name: string | null
    row_span: number
    col_span: number
}

interface ChartPanel {
    id: number
    form: FormGridType<{
        name: string
        cols: number
        rows: number
    }>
    layout: Chart[][]
    showSqlButton: boolean
}

interface ChartAddFormValues extends PanelIdRowCol {
    mode: number
    fromChart?: string
}

interface DraggedChart extends RowCol {
    chart: Chart
}

export default defineComponent({
    components: {
        EditChart,
        Icona,
        FormGrid,
    },
    beforeRouteEnter (to, _from, next: NextWithReload) {
        next((vm) => {
            // Reset state
            vm.init(to.params)
        })
    },
    props: {
        panelId: {
            type: [String],
            required: true,
        },
        rowCol: {
            type: [String],
            default: undefined,
        },
        serieIndex: {
            type: [String],
            default: undefined,
        },
    },
    setup() {
        return {
            titleBreadcrumbs: useTitleBreadcrumbs(),
        }
    },
    data () {
        return {
            inProcess: false,
            panel: undefined as ChartPanel | undefined,
            selectedChart: undefined as RowCol | undefined,
            chartAddForm: {} as FormGridType<ChartAddFormValues>,
            chartForm: {} as FormGridType,
            addSerieForm: {} as FormGridType,
            draggedChart: undefined as DraggedChart | undefined,
            focusOnFilter: undefined as FormValue | undefined,
        }
    },

    computed: {
        selectedPanelIdRowCol() {
            if (!this.selectedChart) {
                return undefined
            }

            return {
                panelId: this.panelId,
                col: this.selectedChart.col,
                row: this.selectedChart.row,
            }
        },
    },

    watch: {
        rowCol: {
            async handler(rowCol: string) {
                const [row, col] = rowCol.split('-').map((v) => parseInt(v))
                this.selectedChart = { row, col }
            },
            immediate: true,
        },
        selectedPanelIdRowCol: {
            async handler (selectedPanelIdRowCol: PanelIdRowCol | undefined) {
                if (!selectedPanelIdRowCol) {
                    return
                }

                const { row, col } = selectedPanelIdRowCol
                if (this.panel!.layout[row][col].name === null) {
                    // New chart form
                    this.chartAddForm = await this.$fetch('/monitoring/charts/chart-add-form', this.selectedPanelIdRowCol)
                } else {
                    // Existing chart form handled by inner component
                }
            },
        },
    },

    methods: {
        async init (params: RouteParams) {
            this.destroyHighchart()
            this.panel = undefined
            this.selectedChart = undefined
            await this.loadPanel(params.panelId!)
            // TODO: handle rowCol and serieIndex
            Object.keys(params).forEach(k => delete params[k])
            this.$historyReplaceParams(params)
        },
        async loadPanel (id?: string) {
            this.panel = await this.$fetch<ChartPanel>('/monitoring/charts/panel-edit', { panelId: id || this.panelId })
            this.titleBreadcrumbs.setTitle('Edit panel: ' + this.panel?.form.values?.name)
        },
        getChartSelectorClasses (row: number, col: number, chart: Chart): Record<string, boolean> {
            let btnColor = 'btn-secondary'
            if (this.draggedChart?.chart && this.draggedChart.chart !== chart) {
                btnColor = 'btn-success'
            } else if (row === this.selectedChart?.row && col === this.selectedChart?.col) {
                btnColor = 'btn-primary'
            }

            return {
                [btnColor]: true,
                'font-italic': chart.name === null,
            }
        },
        async onPanelDelete () {
            if (!confirm('Really delete panel?')) {
                return
            }

            if (!this.panel) {
                return
            }

            return this.$fetch('/monitoring/charts/panel-delete', { panelId: this.panel.id }).then(() => {
                this.$notify({
                    type: 'success',
                    message: 'Panel deleted',
                })
                this.$router.push({ name: 'panel-list' })
            })
        },
        async onPanelChange (params: Values) {
            if (!this.panel || !this.panelId) {
                return
            }
            params.panelId = this.panelId
            await this.$processFormResponse(this.$fetch('/monitoring/charts/panel-save', params), this.panel.form)
            await this.loadPanel()
        },
        onChartSelect (row: number, col: number) {
            this.$historyReplaceParams({ rowCol: `${row}-${col}` })
        },
        async onChartAdd (params: ChartAddFormValues) {
            await this.$processFormResponse(this.$fetch('/monitoring/charts/chart-add', params), this.chartAddForm)
            this.loadPanel().then(() => {
                this.onChartSelect(params.row, params.col)
            })
        },
        async onChartDelete () {
            this.selectedChart = undefined
            await this.loadPanel()
        },
        async loadChart () {
            const conf = await this.$fetch('/monitoring/charts/chart-config', this.selectedPanelIdRowCol)
            this.destroyHighchart()
            const { default: Highcharts } = await HighchartsLoader()
            this.$options.highchart = Highcharts.stockChart('panels-chart-highchart-preview', conf)
            await this.loadChartData(conf)
        },
        async loadChartData (conf: { xAxis: Array<{ range: number }> }) {
            const to = (new Date()).valueOf() - 60 * 1000
            const from = (new Date(to - conf.xAxis[0].range)).valueOf()
            const series = await this.$fetch('/monitoring/charts/chart-data', Object.assign({}, this.selectedPanelIdRowCol, { from, to }))

            series.forEach((serieData: unknown, i: number) => {
                this.$options.highchart.series[i].setData(serieData, false)
            })
            this.$options.highchart.redraw()
        },
        destroyHighchart (): void {
            if (this.$options.highchart) {
                this.$options.highchart.destroy()
            }
        },
        onDragStart (e: DragEvent, row: number, col: number, chart: Chart) {
            this.draggedChart = { chart, row, col }
            // force some browsers enable drag'n'drop
            e.dataTransfer!.setData('text/plain', '')
            e.dataTransfer!.dropEffect = 'move'
        },
        async onDrop (targetRow: number, targetCol: number, chart: Chart) {
            if (!this.draggedChart || chart === this.draggedChart.chart) {
                return
            }
            this.inProcess = true
            this.selectedChart = undefined
            await this.$fetch('/monitoring/charts/chart-switch', {
                panelId: this.panelId,
                row: this.draggedChart.row,
                col: this.draggedChart.col,
                targetRow: targetRow,
                targetCol: targetCol,
            })
            await this.loadPanel().finally(() => this.inProcess = false)
        },
        onDragEnd () {
            this.draggedChart = undefined
        },
    },
})
</script>
