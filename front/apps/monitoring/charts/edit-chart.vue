<template>
    <div class="card mb-4">
        <div class="card-body">
            <h3>
                Chart
                <button
                    type="button"
                    class="btn btn-danger btn-sm"
                    @click="onChartDelete"
                >
                    <Icona name="icn-delete" /> Delete
                </button>
            </h3>
            <FormGrid
                v-bind="chartForm"
                @change="onChartChange"
            />
            <h3>Series</h3>
            <Tabs :tabs="tabs">
                <template #default="{ tab }: { tab: SerieTabOrNewSerieTab }">
                    <template v-if="'isAddSerie' in tab">
                        <FormGrid
                            v-bind="addSerieForm"
                            @change="addSerieForm.values = $event"
                            @submit="onChartNewSerieAdd"
                        />
                    </template>
                    <template v-else-if="tab.serieForm">
                        <FormGrid
                            v-bind="tab.serieForm"
                            @change="onChartSerieChange"
                        />
                        <div class="card-body row px-0">
                            <div class="col-md-6">
                                <FormParams
                                    :errors="tab.errors"
                                    :disabled="inProcessFinal"
                                    :formValues="tab.flatFormValues!"
                                    :inputsSets="tab.inputsSets!"
                                />
                            </div>
                            <div class="col-md-6">
                                <FormList
                                    ref="list"
                                    class="mt-1"
                                    :disabled="inProcessFinal"
                                    :formBlocks="[tab.formValuesBlock!]"
                                    :inputsByName="tab.inputsByName!"
                                    @change="onChange"
                                    @changeOperator="onChangeOperator"
                                    @delete="onDelete"
                                />
                            </div>
                        </div>
                        <div class="input-group">
                            <label class="input-group-text">Metric</label>
                            <Dropdown
                                :enabled="!inProcessFinal"
                                :isInvalid="'metric' in (tab.errors || {})"
                                :value="tab.metric"
                                :allowToggleAll="false"
                                v-bind="tab.metricDropdown"
                                @input="onMetricChanged($event as string | undefined)"
                            />
                            <button
                                type="button"
                                class="btn btn-danger"
                                :disabled="inProcessFinal"
                                @click="onChartSerieDelete()"
                            >
                                <Icona name="icn-delete" /> Delete serie
                            </button>
                            <button
                                v-if="showSqlButton && !hasError"
                                type="button"
                                class="btn btn-outline-warning"
                                :disabled="inProcessFinal"
                                @click="processSqlView(!sql)"
                            >
                                <Icona name="icn-database" /> SQL
                            </button>
                            <button
                                type="button"
                                class="btn btn-success"
                                :disabled="inProcessFinal"
                                @click="onChartSerieClone()"
                            >
                                <Icona name="icn-copy" /> Clone serie
                            </button>
                        </div>
                    </template>
                </template>
            </Tabs>
        </div>
    </div>
    <div class="card">
        <div class="card-body">
            <template v-if="sql && !hasError">
                <h3>SQL</h3>
                <pre
                    class="p-3"
                    style="white-space: pre-line;"
                >{{ sql }}</pre>
            </template>
            <h3>Preview</h3>
            <div
                id="panels-chart-highchart-preview"
                style="min-height: 500px;"
            />
        </div>
    </div>
</template>

<script lang="ts">

import { defineComponent, PropType } from 'vue'
import { FormList, FormParams, FormGrid, HighchartsLoader, Tabs, Dropdown, Icona } from '@/components'
import { $emptyFormValuesBlock, $emptyListParamWithKey, $fromValuesToUrlParams } from '@/utils/form-list-utils'
import { FormGridType, FormBlock, FormInput, FormInputsSet, FormValue, FormValueIndex, Values, Value, Errors, Item, ItemGroup, Tab } from '@/types'
import { PanelIdRowCol } from './edit.vue'

type Serie = {
    name: string
    className: string
    order: number
    source: string
    sourceConfig?: SerieSourceConfig
    nulls_strategy: number
}

interface MetricDropdown {
    items: Item<string>[]
    groups: ItemGroup[]
    multiple: boolean
}

interface SerieSourceConfig {
    metric: string
    filters: [string, Value, string][]
}

interface SerieTab extends Tab {
    serieForm?: FormGridType<Serie>
    metricDropdown?: MetricDropdown
    inputsSets?: FormInputsSet[]
    inputsByName?: Record<string, FormInput>
    metric?: string
    flatFormValues?: FormValue[]
    formValuesBlock?: FormBlock
    errors?: Errors
}

interface SerieNewTab extends Tab {
    isAddSerie: true
}

type ChartSeriesResponse = {
    series: {
        serie: FormGridType<Serie>
        sourceConfig: SerieSourceConfig
        inputsSets: FormInputsSet[]
        metricDropdown: MetricDropdown
    }[]
}

type SerieTabOrNewSerieTab = SerieTab | SerieNewTab

export default defineComponent({
    components: {
        Icona,
        FormList,
        FormParams,
        Tabs,
        FormGrid,
        Dropdown,
    },
    props: {
        panelRowCol: {
            type: Object as PropType<PanelIdRowCol>,
            required: true,
        },
        inProcess: {
            type: Boolean,
            required: true,
        },
        showSqlButton: {
            type: Boolean,
            required: true,
        },
    },
    emits: {
        'chartDelete': null,
        'panelUpdate': null,
    },
    data () {
        return {
            chartForm: {} as FormGridType,
            addSerieForm: {} as FormGridType,
            tabs: [] as SerieTabOrNewSerieTab[],
            hasError: false,
            sql: undefined as string | undefined,
            activeTab: 0,
            inProcessChart: false,
        }
    },

    computed: {
        selectedChartSerieId() {
            return {
                ...this.panelRowCol,
                serieIndex: this.activeTab,
            }
        },
        inProcessFinal () {
            return this.inProcess || this.inProcessChart
        },
        activeSerieTab() {
            const tab = this.tabs[this.activeTab]

            if ('isAddSerie' in tab) {
                return
            }

            return tab
        },
    },
    watch: {
        panelRowCol: {
            async handler() {
                await this.loadAll()
            },
            immediate: true,
        },
    },

    mounted () {
        this.resetTabs()
    },

    methods: {
        async loadAll() {
            return Promise.all([
                this.loadChartForm(),
                this.loadAndDrawHighChart(),
                this.$fetch('/monitoring/charts/serie-add-form', this.panelRowCol).then((form: FormGridType) => {
                    this.addSerieForm = form
                }),
            ])
        },
        async onChartDelete () {
            if (!confirm('Really delete chart?')) {
                return
            }

            await this.$fetch('/monitoring/charts/chart-delete', this.panelRowCol)

            this.$emit('chartDelete')
        },
        flatFormFiltersToBlock (formFilters: FormValue[], inputsSets: FormInputsSet[]): FormBlock {
            const inputsNamesToSetIndex: Record<string, number> = {}
            inputsSets.forEach((set, setIndex) => set.inputs.forEach(input => {
                inputsNamesToSetIndex[input.name] = setIndex
            }))

            const block = $emptyFormValuesBlock(inputsSets)
            formFilters.forEach(f => {
                block.blockSets[inputsNamesToSetIndex[f.name]].values.push(f)
            })
            return block
        },
        inputsByName (inputsSets: FormInputsSet[]): Record<string, FormInput> {
            const inputs: Record<string, FormInput> = {}
            inputsSets.forEach(set => set.inputs.forEach(input => {
                inputs[input.name] = input
            }))
            return inputs
        },
        async loadChartForm () {
            try {
                this.chartForm = await this.$fetch('/monitoring/charts/chart-edit-form', this.panelRowCol)
            } catch (e) {
                this.$notify({
                    type: 'error',
                    message: 'Chart form loading failed! Danger! Can lead to series config loss! Do not edit',
                })
                return
            }

            try {
                const r = await this.$fetch<ChartSeriesResponse>('/monitoring/charts/chart-series', this.panelRowCol)
                this.resetTabs()
                const { panelId, row, col } = this.panelRowCol
                r.series.reverse().forEach((serie, i) => {
                    const flatFormValues = serie.sourceConfig.filters.map(([name, value, operator]: [string, Value, string]) => ({
                        ...$emptyListParamWithKey(),
                        name,
                        value,
                        operator,
                    }))

                    this.tabs.unshift({
                        route: {
                            name: 'chart-edit',
                            params: {
                                panelId: String(panelId),
                                rowCol: row + '-' + col,
                                serieIndex: String(i),
                            },
                        },
                        title: serie.serie.values?.name || 'No name serie',
                        serieForm: serie.serie,
                        metricDropdown: serie.metricDropdown,
                        inputsSets: serie.inputsSets,
                        inputsByName: this.inputsByName(serie.inputsSets),
                        metric: serie.sourceConfig.metric,
                        flatFormValues,
                        formValuesBlock: this.flatFormFiltersToBlock(flatFormValues, serie.inputsSets),
                    })
                })
            } catch (e) {
                this.$notify({
                    type: 'error',
                    message: 'Chart series loading failed! Danger! Can lead to series config loss! Do not edit',
                })
            }
        },
        async onChartChange (chartConfig: Values) {
            const params = Object.assign({ config: chartConfig }, this.panelRowCol)
            delete params.config.series
            this.$processFormResponse(this.$fetch('/monitoring/charts/chart-save', params), this.chartForm as FormGridType).then(() => {
                this.$emit('panelUpdate')
                this.loadChartForm()
            }).catch(() => this.hasError = true)
        },
        processSqlView (show: boolean) {
            if (!show) {
                this.sql = undefined
                return
            }
            interface ChartConfig {
                xAxis: Array<{ range: number }>
            }
            return show && this.$fetch<ChartConfig>('/monitoring/charts/chart-config', this.panelRowCol).then((conf: ChartConfig) => {
                const to = (new Date()).valueOf() - 60 * 1000
                const from = (new Date(to - conf.xAxis[0].range)).valueOf()
                return this.loadSql(from, to)
            })
        },
        async loadSql (from: number, to: number) {
            this.sql = undefined
            const data = await this.$fetch<{ sql: string }>('/monitoring/charts/chart-serie-sql', Object.assign({}, this.panelRowCol, this.selectedChartSerieId, { from, to }))
            this.sql = data.sql
        },
        async loadAndDrawHighChart () {
            const conf = await this.$fetch('/monitoring/charts/chart-config', this.panelRowCol)
            this.destroyHighchart()
            const { default: Highcharts } = await HighchartsLoader()
            this.$options.highchart = Highcharts.stockChart('panels-chart-highchart-preview', conf)
            const series = await this.loadChartData(conf)
            series.forEach((serieData: unknown, i: number) => {
                this.$options.highchart.series[i].setData(serieData, false)
            })
            this.$options.highchart.redraw()
            this.hasError = false
        },
        async loadChartData (conf: { xAxis: Array<{ range: number }> }) {
            const to = (new Date()).valueOf() - 60 * 1000
            const from = (new Date(to - conf.xAxis[0].range)).valueOf()
            if (this.sql !== undefined) {
                await this.loadSql(from, to)
            }
            return this.$fetch('/monitoring/charts/chart-data', Object.assign({}, this.panelRowCol, { from, to }))
        },
        resetTabs () {
            this.activeTab = 0
            this.tabs = [
                {
                    title: 'Add new serie',
                    isAddSerie: true,
                },
            ]
        },
        destroyHighchart () {
            if (this.$options.highchart) {
                this.$options.highchart.destroy()
            }
        },
        onChartNewSerieAdd (source: Values) {
            const params = Object.assign(source, this.panelRowCol)
            this.$processFormResponse(this.$fetch('/monitoring/charts/serie-add', params), this.addSerieForm).then(() => {
                this.loadChartForm().then(() => {
                    this.activeTab = Math.max(0, this.tabs.length - 2)
                    this.loadAndDrawHighChart()
                })
            })
        },
        async onChartSerieDelete () {
            if (!confirm('Really delete serie?')) {
                return
            }

            await this.$fetch('/monitoring/charts/serie-delete', this.selectedChartSerieId)
            await this.loadChartForm()
            await this.loadAndDrawHighChart()
        },
        async onChartSerieClone () {
            await this.$fetch('/monitoring/charts/serie-clone', this.selectedChartSerieId)
            await this.loadChartForm()
            await this.loadAndDrawHighChart()
        },
        async onChartSerieChange (serie: Serie) {
            const params = Object.assign({ config: serie }, this.selectedChartSerieId)
            delete params.config.sourceConfig

            await this.$processFormResponse<Serie>(this.$fetch('/monitoring/charts/serie-save', params), this.activeSerieTab.serieForm!)
            await this.loadChartForm()
            await this.loadAndDrawHighChart()
        },
        onChange ({ valueIndex, value }: { valueIndex: FormValueIndex, value: Value }): void {
            this.activeSerieTab!.formValuesBlock!.blockSets[valueIndex.setIndex].values[valueIndex.valueIndex].value = value
            this.onValuesChanged()
        },
        onChangeOperator ({ valueIndex, operator }: { valueIndex: FormValueIndex, operator: string }): void {
            this.activeSerieTab!.formValuesBlock!.blockSets[valueIndex.setIndex].values[valueIndex.valueIndex].operator = operator
            this.onValuesChanged()
        },
        onDelete (valueIndex: FormValueIndex): void {
            this.activeSerieTab!.formValuesBlock!.blockSets[valueIndex.setIndex].values.splice(valueIndex.valueIndex, 1)
            this.onValuesChanged()
        },
        onMetricChanged (value: string | undefined): void {
            this.activeSerieTab!.metric = value
            this.onValuesChanged()
        },
        onValuesChanged (): void {
            const curTab = this.activeSerieTab!
            curTab.flatFormValues = curTab.formValuesBlock!.blockSets.map(s => s.values).flat()
            const params = {
                ...this.selectedChartSerieId,
                chartConfig: { ...$fromValuesToUrlParams(curTab.flatFormValues), ...{ metric: curTab.metric } },
            }

            interface ErrorResponse {
                status: number
                json(): Promise<{ errors?: Errors }>
            }

            this.inProcessChart = true
            this.$fetch('/monitoring/charts/source-save', params).then(() => {
                delete curTab.errors
                curTab.flatFormValues!.forEach(param => delete param.error)
                return this.loadAndDrawHighChart()
            })
                .catch((resp: ErrorResponse) => {
                    this.hasError = true
                    if (resp.status !== 422) {
                        return
                    }

                    return resp.json().then(data => {
                        if (!('errors' in data)) {
                            return
                        }

                        curTab.errors = data.errors
                        curTab.flatFormValues!.forEach((param, index) => {
                            const key = `f${index}e`

                            if (data.errors && key in data.errors) {
                                param.error = data.errors[key]
                            } else if (data.errors && param.name in data.errors) {
                                param.error = data.errors[param.name]
                            } else {
                                delete param.error
                            }
                        })
                    })
                })
                .then(() => this.inProcessChart = false)
        },
    },
})
</script>
