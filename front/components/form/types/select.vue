<template>
    <Dropdown
        ref="dropdown"
        :items="list"
        :value="value"
        :groups="groups"
        :placeholder="placeholder"
        :multiple="multiple"
        :size="size"
        :enabled="enabled"
        :isInvalid="isInvalid"
        :allowSearch="allowSearch"
        :allowToggleAll="allowToggleAll"
        :allowToggleNone="allowToggleNone"
        :liveSearchUrl="liveSearchUrl"
        :liveSearchNameAsId="liveSearchNameAsId"
        @input="handleChange"
    />
</template>

<script lang="ts" setup>
import { nextTick, useTemplateRef } from 'vue'
import Dropdown from './../../dropdown.vue'
import { Item, ItemGroup, BsSize } from '@/types'

type DropdownAnyValue = T | T[] | null

defineOptions({
    inheritAttrs: false,
})

withDefaults(defineProps<{
    enabled?: boolean
    list?: DItem[]
    value?: DropdownAnyValue
    placeholder?: string
    selectorNone?: boolean
    groups?: ItemGroup[]
    multiple?: boolean
    size?: BsSize
    isInvalid?: boolean
    liveSearchUrl?: string
    liveSearchNameAsId?: boolean
    allowSearch?: boolean
    allowToggleAll?: boolean
    allowToggleNone?: boolean
}>(), {
    enabled: true,
    list: () => [],
    value: undefined,
    placeholder: '...',
    selectorNone: true,
    groups: () => [],
    multiple: true,
    size: 'md',
    liveSearchUrl: undefined,
    allowSearch: true,
    allowToggleAll: true,
    allowToggleNone: true,
})

const $emit = defineEmits<{
    change: [value: DropdownAnyValue]
}>()

const dropdown = useTemplateRef('dropdown')

function handleChange(value: DropdownAnyValue): void {
    $emit('change', value)
}

function focus(): void {
    nextTick(() => dropdown.value?.openDropdown())
}

defineExpose({
    focus,
})
</script>
