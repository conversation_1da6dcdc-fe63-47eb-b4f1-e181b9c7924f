import { ComponentPublicInstance, CSSProperties, ShallowReactive, type TdHTMLAttributes } from 'vue'
import { LocationQuery, RouteLocationRaw } from 'vue-router'
import { universalSort } from '@/components/entity-table/table.vue'

export type Id = string | number
export type ClassList = Record<string, boolean>
export type BsSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl'

export interface SiteIdUserId {
    siteId: number
    userId: number
}

export interface Item<ID = Id> {
    id: ID
    name: string
    group?: string
    count?: number
    disabled?: boolean
}

export interface ItemGroup {
    name: string
    style?: string
}

export type TableRow = Record<string, unknown>;

export interface TableColumn {
    name: string
    code: string
    slotName?: string
    hidden?: boolean
    raw?: boolean
    sortable?: boolean
    important?: boolean
    style?: CSSProperties
    nowrap?: boolean
    longTextBrake?: boolean
    align?: string
}

export type TableType = {
    rowKeyName?: string
    tableClasses?: Record<string, boolean>
    columns?: TableColumn[]
    sort?: string
    sortCallback?: typeof universalSort
    enableInnerSort?: boolean
    data?: TableRow[] | ShallowReactive<TableRow[]>
    total?: number
    rowProps?: (row: TableRow) => TdHTMLAttributes
    disabled?: boolean
    showHeader?: boolean
    showRowsCount?: boolean
    sticky?: boolean
};

export interface DropdownType {
    items: Item[]
    groups: ItemGroup[]
    multiple?: boolean
}

export interface BtnGroupItem extends Item {
    class?: ClassList | string
    activeClass?: string
    icon?: string
    title?: string
}

export type ScalarValue = Id | boolean
export type ScalarOrEmpty = ScalarValue | undefined | null
export type Value = ScalarOrEmpty | ScalarValue[]
export type Values = Record<string, Value>
export type ValuesOrFileList = Record<string, Value | FileList>

export interface TableStyleValue {
    text: string
    style?: CSSProperties
    title?: string
}

export type Errors = Record<string, string>

export interface FormElement {
    width: number
    name: string
    title: string
    type: string
    size?: BsSize
    required?: boolean
    enabled?: boolean
    tooltip?: string
    hint?: string
    slotName?: string
    operators: Item<string>[]
    operatorName?: string
    operatorPostfix?: string
    units: Item<string>[]
    unitPostfix?: string
    hideLabel?: boolean
}

export interface FormComponentMinimal {
    enabled?: boolean
    size?: BsSize
    required?: boolean
    isInvalid?: boolean
}

export interface FormComponentCommon extends FormComponentMinimal {
    id: string
    value: Value
}

export interface FormGridType<V = Values> {
    uniqueId?: string
    enabled?: boolean
    blocks?: FormElement[][]
    values?: V
    errors?: Errors
    enctype?: 'json' | 'formData'
    keepFocus?: boolean
    submitOnEnter?: boolean
    title?: string
    size?: BsSize
}

export interface FormBlockSet {
    title: string
    show?: boolean
    values: FormValue[]
}

export interface FormBlock {
    blockSets: FormBlockSet[]
}

export interface FormValueWithBlock extends FormValue {
    orBlock: number
}

export interface FormValue {
    name: string
    key: symbol
    operator?: string
    error?: string
    value?: Value
}

export interface FormInput {
    name: string
    title?: string
    width?: number
    operators?: Item<string>[]
    operatorComponent?: Values
    props?: {
        type?: string
        liveSearchUrl?: string
        isRange?: boolean
        hint?: string
        // Append new props on occasion
    }
}

export interface FormValueIndex {
    blockIndex: number
    setIndex: number
    valueIndex: number
}

export interface SortableFormEvent {
    valueName: string
    from: FormValueIndex
    to: FormValueIndex
}

export interface FormInputsSet {
    title: string
    inputs: FormInput[]
}

export interface PaginationType {
    lastPage?: number
    page?: number
    pageSize?: number
    firstItemsNum?: number
    disabled?: boolean
}

export interface RichTableType {
    form?: FormGridType
    table?: TableType
    pagination?: PaginationType
    title?: string
    showTitle?: boolean
    showTotal?: boolean
    showPagination?: boolean
    showRefresh?: boolean
    showReset?: boolean
    showTableHeader?: boolean
    disabled?: boolean
    tableClasses?: Record<string, boolean>
    reloadOnChange?: boolean
    size?: BsSize
    sticky?: boolean
}

export interface MenuItem {
    label: string
    url?: string
    isMega?: boolean
    items?: MenuItem[]
    isFavorite?: boolean
}

export interface Tab {
    title: string
    route?: RouteLocationRaw & {
        name: string
        params?: Record<string, string>
    }
    badge?: string
}

export interface DocFile {
    url: string
    siteId: number
    userId: number
    filename: string
    processing?: boolean
    id?: number
    siteUser?: string
    tags: string[]
    country?: string
    date?: string
    needApprove?: boolean
    selfieQuality?: number
    docQuality?: number
    tagsExternal?: string[]
    isDeleted?: number
    aiValidation?: {
        valid: boolean
        messages?: string[]
    }
}

export interface WithReload extends ComponentPublicInstance {
    $decodeParams (query: LocationQuery): Values /** @see ./utils/url-params.ts */
    reload (params: Values): Promise<unknown>
    init (params: Values): Promise<unknown>
}

export type NextWithReload = (nextHandler: (vm: WithReload) => void) => void

/** @see AuthController::actionConfig() */
export interface UserAuthConfig {
    menu: MenuItem[]
    user: {
        isPasswordExpiring: boolean
        shortName: string
    }
    can: {
        editHelp: boolean
        faceMatching: boolean
        faceValidation: boolean
    }
    nodeName: string
    lastRequest: {
        hasFeedback: boolean
        message: string
        feedback: string
    }
}

export interface Help {
    id: number
    url: string
    description: string
    elements: Record<string, string>
}

export type NotifyMessage = string | {
    message?: string
    type?: 'success' | 'warning' | 'error' | 'info'
    duration?: number
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type FetchType = <T = any>(url: string, params?: object | FormData, stringify?: boolean) => Promise<T>
export type NotifyType = (notification: NotifyMessage) => void

export type NormalizeParamsObjType = (params: Values, encodeParts?: boolean, skipEmptyString?: boolean) => Record<string, string | number>
export type HistoryChangeParamsType = (params: Values, skipEmptyString?: boolean) => void
export type EncodeParamsType = (params: Values) => string
export type DecodeParamsType = (query: LocationQuery) => Values

export interface PointType { x: number, y: number }

export type ExtractType<T extends Promise<unknown>> = T extends Promise<infer A> ? A : unknown
export type RequiredBy<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>

// https://github.com/vuejs/language-tools/blob/master/packages/component-type-helpers/index.ts
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type ComponentProps<T> = T extends new(...args: any) => { $props: infer P } ? NonNullable<P> : T extends (props: infer P, ...args: any) => any ? P : object
export type CamelCase<S extends string> = S extends `${ infer P1 }_${ infer P2 }${ infer P3 }` ? `${ Lowercase<P1> }${ Uppercase<P2> }${ CamelCase<P3> }` : Lowercase<S>
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type KeysToCamelCase<T> = { [K in keyof T as CamelCase<string & K>]: T[K] extends Record<string, any> ? KeysToCamelCase<T[K]> : T[K] }
