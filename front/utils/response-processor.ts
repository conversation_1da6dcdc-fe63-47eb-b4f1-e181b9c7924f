/**
 * This mixin serves for processing server responses (promises).
 *  A primary focus is on processing form and rich table responses.
 */

import { ExtractType, FormGridType, RichTableType, TableRow, Values } from '@/types'
import { shallowReactive } from 'vue'

export function $processResponse<T, E = unknown>(promise: Promise<T>, onInvalid: (data: E) => void): typeof promise {
    return promise
        .catch(response => {
            if (response.status === 422) {
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                return response.json().then((data: any) => {
                    onInvalid(data)

                    // Hack to allow to get J<PERSON><PERSON> another time;
                    const altResponse = {
                        ok: response.ok,
                        header: response.headers,
                        status: response.status,
                        json: () => new Promise((resolve) => resolve(data)),
                    }

                    return Promise.reject(altResponse)
                })
            }

            return Promise.reject(response)
        })
}

export function $processFormResponse<V, FV = Values, T extends FormGridType<FV> = FormGridType<FV>>(promise: Promise<V>, form: T, onInvalidSetForm?: (data: T) =>  void): Promise<V> {
    form.errors = {}
    form.enabled = false

    if (onInvalidSetForm === undefined) {
        onInvalidSetForm = data => $setForm(form, data)
    }

    return $processResponse(promise, onInvalidSetForm)
        .finally(() => {
            form.enabled = true
        })
}

export function $processRichTableResponse<T extends RichTableType>(promise: Promise<T>, table: T, autoDisable: boolean = true): typeof promise {
    if (table.form) {
        table.form.errors = {}
    }

    const onInvalid = (data: ExtractType<typeof promise>) => {
        if (data.form?.errors) {
            if (!table.form) {
                table.form = {}
            }
            table.form.errors = data.form.errors
        }
    }

    if (autoDisable) {
        table.disabled = true
    }

    return $processResponse(promise, onInvalid)
        .then(data => {
            if (typeof data === 'object') {
                (['form', 'table', 'pagination', 'title'] as (keyof typeof table)[]).forEach(key => {
                    if (key in data) {
                        if (key === 'table' && data.table) {
                            const rows = data.table.data as unknown as TableRow[];
                            data.table.data = shallowReactive(rows)
                        }
                        table[key] = data[key] as never
                    }
                })
            }

            return data
        })
        .finally(() => {
            if (autoDisable) {
                table.disabled = false
            }
        }) as typeof promise
}

export function $setForm<V, T extends FormGridType<V>>(form: FormGridType<V>, data: T): void {
    if (typeof data === 'object' && data !== null) {
        // Setting only props, available in form-grid
        (['blocks', 'values', 'errors', 'size', 'title'] as (keyof typeof form)[]).forEach(key => {
            if (key in data) {
                form[key] = data[key] as never
            }
        })
    }
}

export function useProcessResponse() {
    return $processResponse
}

export function useProcessRichTableResponse() {
    return $processRichTableResponse
}

export function useProcessFormResponse() {
    return $processFormResponse
}

export function useSetForm() {
    return $setForm
}
