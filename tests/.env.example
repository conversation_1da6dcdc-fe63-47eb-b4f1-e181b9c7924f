APP_DEBUG=1
APP_ENV=test

COOKIE_VALIDATION_KEY=12345

BASE_URL=http://nginx:81/

MAILER_USE_FILE_TRANSPORT=1
USE_SECURE_COOKIE=0

S3_ENDPOINT_URL=http://minio-test:9010
S3_KEY=analytics-test
S3_SECRET=analytics
S3_BUCKET=analytics

CRON_USER=www-data

# DB config
DB_HOST=postgres-test
DB_NAME=analytics
DB_USER=analytics
DB_PASS=analytics
DB_PORT=5432

DB_SLAVE_HOST=postgres-test
DB_SLAVE_NAME=analytics
DB_SLAVE_USER=analytics
DB_SLAVE_PASS=analytics
DB_SLAVE_PORT=5432

# Redis Pool (hosts comma separated)
REDIS_HOSTS=redis-sentinel-test:26379
REDIS_DB=0
REDIS_MASTER=mymaster

# Telegram bot service
TG_ENDPOINT=http://php:1234
TG_KEY=analytics

# Auth key for all analytics api clients
API_AUTH_KEY=analytics-test

CRM_HOST=http://php:1234
CRM_PROJECT=123
CRM_AUTH_KEY=analytics

# Element chat bot
ELEMENT_ENDPOINT=http://php:1234
ELEMENT_KEY=analytics

MOCK_URL=http://php:1234

