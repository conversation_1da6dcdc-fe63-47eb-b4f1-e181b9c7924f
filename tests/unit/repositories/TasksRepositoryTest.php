<?php

declare(strict_types=1);

namespace app\tests\unit\repositories;

use app\back\entities\Task;
use app\back\modules\task\components\QueueScheduler;
use app\back\repositories\TaskQueues;
use app\back\repositories\Tasks;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class TasksRepositoryTest extends TestCase
{
    use DbTransactionalUnitTrait;

    private const int PERIOD_CUR_DAY_7_HOURS_UTC = 1;
    private const int PERIOD_CUR_DAY_7_HOURS_EEST = 2;
    private const int PERIOD_PREV_DAY_7_HOURS = 3;

    private const string QUEUE = 'TEST_QUEUE';

    public function testBasic(): void
    {
        /** @var Tasks $tasksRepo */
        $tasksRepo = $this->repo(Tasks::class);
        /** @var TaskQueues $taskQueuesRepo */
        $taskQueuesRepo = $this->repo(TaskQueues::class);

        $from = new \DateTimeImmutable('2022-07-04 10:00:00');
        $period = new \DateInterval('P1D');

        $taskEntity = $tasksRepo->create(Task::SOURCE_CLI, 'task1', 'RES', $from, $period, [], ['task2']);
        $taskQueuesRepo->create($taskEntity->id, self::QUEUE);

        $this->seeRecordWithFields(Tasks::class, [
            'name' => 'task1',
            'resource' => 'RES',
        ], [
            'source' => Task::SOURCE_CLI,
            'from' => $from,
            'period' => new \DateInterval('P1D'),
            'dependencies' => ['task2'],
        ]);

        $dependencies = Tasks::formatDependenciesInLine($tasksRepo->getUnfulfilledDependencies($taskEntity));
        self::assertEquals(['task2 RES 2022-07-04 10:00:00 P1D'], $dependencies);

        $taskEntity2 = $tasksRepo->create(Task::SOURCE_CLI, 'task2', 'RES', $from, $period);
        $taskQueuesRepo->create($taskEntity2->id, self::QUEUE);

        $this->seeRecordWithFields(Tasks::class, [
            'name' => 'task2',
            'resource' => 'RES',
        ], [
            'source' => Task::SOURCE_CLI,
            'from' =>  $from,
            'period' => new \DateInterval('P1D'),
        ]);

        $dependencies = Tasks::formatDependenciesInLine($tasksRepo->getUnfulfilledDependencies($taskEntity));
        self::assertEquals(['task2 RES 2022-07-04 10:00:00 P1D'], $dependencies);

        $taskEntity2->error = true;
        $taskEntity2->waiting = true;
        $tasksRepo->update($taskEntity2, ['error', 'waiting']);

        $dependencies = Tasks::formatDependenciesInLine($tasksRepo->getUnfulfilledDependencies($taskEntity));
        self::assertEquals(['task2 RES 2022-07-04 10:00:00 P1D'], $dependencies); // Not ended, error and waiting

        $taskEntity2->ended = true;
        $tasksRepo->update($taskEntity2, ['ended']);

        $dependencies = Tasks::formatDependenciesInLine($tasksRepo->getUnfulfilledDependencies($taskEntity));
        self::assertEquals(['task2 RES 2022-07-04 10:00:00 P1D'], $dependencies); // Ended, but still with error and waiting

        $taskEntity2->error = false;
        $tasksRepo->update($taskEntity2, ['error']);

        $dependencies = Tasks::formatDependenciesInLine($tasksRepo->getUnfulfilledDependencies($taskEntity));
        self::assertEquals(['task2 RES 2022-07-04 10:00:00 P1D'], $dependencies); // Ended without error, but still waiting for dependencies

        $taskEntity2->waiting = false;
        $tasksRepo->update($taskEntity2, ['waiting']);

        $dependencies = Tasks::formatDependenciesInLine($tasksRepo->getUnfulfilledDependencies($taskEntity));
        self::assertEquals([], $dependencies); // All checks passed
    }

    #[DataProvider('dataGaps')]
    public function testGaps(string $from, string $period, array $dependencies, $expectedGaps): void
    {
        /** @var Tasks $tasksRepo */
        $tasksRepo = $this->repo(Tasks::class);
        /** @var TaskQueues $taskQueuesRepo */
        $taskQueuesRepo = $this->repo(TaskQueues::class);

        $taskEntity = $tasksRepo->create(Task::SOURCE_CLI, 'task1', 'RES', new \DateTimeImmutable($from), new \DateInterval($period), [], ['task2']);
        $taskQueuesRepo->create($taskEntity->id, self::QUEUE);

        $gaps = Tasks::formatDependenciesInLine($tasksRepo->getUnfulfilledDependencies($taskEntity));
        self::assertEquals(["task2 RES $from $period"], $gaps);

        foreach ($dependencies as [$depFrom, $depPeriod]) {
            $taskEntity2 = $tasksRepo->create(Task::SOURCE_CLI, 'task2', 'RES', new \DateTimeImmutable($depFrom), new \DateInterval($depPeriod));
            $taskQueuesRepo->create($taskEntity2->id, self::QUEUE);
            $taskEntity2->ended = true;
            $taskEntity2->error = false;
            $tasksRepo->update($taskEntity2, ['ended', 'error']);
        }

        self::assertEquals($expectedGaps, Tasks::formatDependenciesInLine($tasksRepo->getUnfulfilledDependencies($taskEntity)));
    }

    public static function dataGaps(): array
    {
        return [
            ['2022-07-04 10:00:00', 'P1D', [['2022-07-04 10:00:00', 'P1D']], []],
            ['2022-07-04 10:00:00', 'P1D', [['2022-07-04 09:00:00', 'P2D']], []],
            ['2022-07-04 10:00:00', 'PT1S', [['2022-07-04 10:00:00', 'PT1S']], []],

            ['2022-07-04 10:00:00', 'PT1S', [['2022-07-04 09:59:59', 'PT1S']], ['task2 RES 2022-07-04 10:00:00 PT1S']],
            ['2022-07-04 10:00:00', 'PT1S', [['2022-07-04 10:00:01', 'PT1S']], ['task2 RES 2022-07-04 10:00:00 PT1S']],
            ['2022-07-04 10:00:00', 'PT3H', [['2022-07-04 11:00:00', 'PT1H']], ['task2 RES 2022-07-04 10:00:00 PT1H', 'task2 RES 2022-07-04 12:00:00 PT1H']],
            ['2022-07-04 10:00:00', 'PT10H', [['2022-07-04 05:00:00', 'PT10H']], ['task2 RES 2022-07-04 15:00:00 PT5H']],

            ['2022-07-04 10:00:00', 'PT2H', [['2022-07-04 10:00:00', 'PT1H'], ['2022-07-04 11:00:00', 'PT1H']], []],
            ['2022-07-04 10:00:00', 'PT3H', [['2022-07-04 09:00:00', 'PT2H'], ['2022-07-04 12:00:00', 'PT1H']], ['task2 RES 2022-07-04 11:00:00 PT1H']],
        ];
    }

    #[DataProvider('dataTwoDeps')]
    public function testTwoDeps(string $from, string $period, array $dependencies, array $expectedGaps): void
    {
        /** @var Tasks $tasksRepo */
        $tasksRepo = $this->repo(Tasks::class);
        /** @var TaskQueues $taskQueuesRepo */
        $taskQueuesRepo = $this->repo(TaskQueues::class);

        $taskEntity = $tasksRepo->create(Task::SOURCE_CLI, 'task1', 'RES', new \DateTimeImmutable($from), new \DateInterval($period), [], ['task2', 'task3']);
        $taskQueuesRepo->create($taskEntity->id, self::QUEUE);

        $gaps = Tasks::formatDependenciesInLine($tasksRepo->getUnfulfilledDependencies($taskEntity));
        self::assertEquals(["task2 RES $from $period", "task3 RES $from $period"], $gaps);

        foreach ($dependencies as [$task, $depFrom, $depPeriod]) {
            $taskEntity2 = $tasksRepo->create(Task::SOURCE_CLI, $task, 'RES', new \DateTimeImmutable($depFrom), new \DateInterval($depPeriod));
            $taskQueuesRepo->create($taskEntity2->id, self::QUEUE);
            $taskEntity2->ended = true;
            $taskEntity2->error = false;
            $tasksRepo->update($taskEntity2, ['ended', 'error']);
        }

        self::assertEquals($expectedGaps, Tasks::formatDependenciesInLine($tasksRepo->getUnfulfilledDependencies($taskEntity)));
    }

    public static function dataTwoDeps(): array
    {
        return [
            ['2022-07-04 10:00:00', 'P1D', [['task2', '2022-07-04 10:00:00', 'P1D'], ['task3', '2022-07-04 10:00:00', 'P1D']], []],
            ['2022-07-04 10:00:00', 'P1D', [['task2', '2022-07-04 10:00:00', 'P1D']], ['task3 RES 2022-07-04 10:00:00 P1D']],
            ['2022-07-04 10:00:00', 'P1D', [['task3', '2022-07-04 10:00:00', 'P1D']], ['task2 RES 2022-07-04 10:00:00 P1D']],
            ['2022-07-04 10:00:00', 'P1D', [['task2', '2022-07-04 15:00:00', 'PT1H'], ['task3', '2022-07-04 20:00:00', 'PT1H']], [
                'task2 RES 2022-07-04 10:00:00 PT5H',
                'task2 RES 2022-07-04 16:00:00 PT18H',
                'task3 RES 2022-07-04 10:00:00 PT10H',
                'task3 RES 2022-07-04 21:00:00 PT13H',
            ]],
        ];
    }

    #[DataProvider('dataCustomResources')]
    public function testCustomResources(string $from, string $period, array $dependencies, array $completed, $expectedGaps): void
    {
        $tasksRepo = $this->repo(Tasks::class);
        $taskQueuesRepo = $this->repo(TaskQueues::class);

        $task = $tasksRepo->create(Task::SOURCE_CLI, 'task1', 'RES', new \DateTimeImmutable($from), new \DateInterval($period), [], $dependencies);
        $taskQueuesRepo->create($task->id, self::QUEUE);

        foreach ($completed as [$depTask, $depRes, $depFrom, $depPeriod]) {
            $depTask = $tasksRepo->create(Task::SOURCE_CLI, $depTask, $depRes, new \DateTimeImmutable($depFrom), new \DateInterval($depPeriod));
            $taskQueuesRepo->create($depTask->id, self::QUEUE);
            $depTask->ended = true;
            $depTask->error = false;
            $tasksRepo->update($depTask, ['ended', 'error']);
        }

        $actual = Tasks::formatDependenciesInLine($tasksRepo->getUnfulfilledDependencies($task));
        sort($actual);
        self::assertEquals($expectedGaps, $actual);
    }

    public function testSchedule(): void
    {
        $limitedConfig = [
            'cronPeriods' => [                       //m h dom mon dow   from shift     period    priority  EEST
                self::PERIOD_PREV_DAY_7_HOURS     => ['0 7 * * *',       '-1 day',      'P1D',    50,       1],
                self::PERIOD_CUR_DAY_7_HOURS_UTC  => ['0 7 * * *',       'today',       'P1D',    50,       0],
                self::PERIOD_CUR_DAY_7_HOURS_EEST => ['0 7 * * *',       'today',       'P1D',    50,       1],
            ],
            'schedule' => [
                self::PERIOD_PREV_DAY_7_HOURS => [
                    'task_1' => ['RES_1'],
                ],
                self::PERIOD_CUR_DAY_7_HOURS_UTC => [
                    'task_2' => ['RES_2'],
                ],
                self::PERIOD_CUR_DAY_7_HOURS_EEST => [
                    'task_3' => ['RES_3'],
                ],
            ],
        ];

        $scheduler = $this->container()->get(QueueScheduler::class);
        $scheduler->configure(Task::SOURCE_CRON, $limitedConfig, [
            'RES_1' => ['RES_1'],
            'RES_2' => ['RES_2'],
            'RES_3' => ['RES_3'],
        ]);

        $scheduler->addTasksToQueue(strtotime('2023-04-06 03:00:00'));

        $this->dontSeeRecords(Tasks::class, [
            ['resource' => 'RES_1'],
            ['resource' => 'RES_2'],
            ['resource' => 'RES_3'],
        ]);

        $scheduler->addTasksToQueue(strtotime('2023-04-06 04:00:00'));

        $this->dontSeeRecord(Tasks::class, ['resource' => 'RES_2']);
        $this->seeRecords(Tasks::class, [
            ['resource' => 'RES_1', 'from' => '2023-04-05 04:00:00'],
            ['resource' => 'RES_3', 'from' => '2023-04-06 00:00:00'],
        ]);

        $this->db()->createCommand()->delete(Tasks::TABLE_NAME)->execute();

        $scheduler->addTasksToQueue(strtotime('2023-04-06 07:00:00'));

        $this->dontSeeRecords(Tasks::class, [
            ['resource' => 'RES_1'],
            ['resource' => 'RES_3'],
        ]);
        $this->seeRecord(Tasks::class, ['resource' => 'RES_2', 'from' => '2023-04-06 00:00:00']);
    }

    public static function dataCustomResources(): array
    {
        return [
            ['2022-07-04 10:00:00', 'P1D', ['task2' => 'RES2'], [['task2', 'RES2', '2022-07-04 08:00:00', 'PT6H']], ['task2 RES2 2022-07-04 14:00:00 PT20H']],
            [
                '2022-07-04 10:00:00', 'PT10M',
                ['task2' => 'RES2', 'task3' => ['RES3', 'RES4'], 'task4'],
                [
                    ...array_map(static fn($v) => ['task2', 'RES2', "2022-07-04 10:0$v:00", 'PT1M'], range(0, 9)),
                    ['task3', 'RES3', "2022-07-04 10:00:00", 'PT5M'],
                    ['task3', 'RES3', "2022-07-04 10:05:00", 'PT5M'],
                    ...array_map(static fn($v) => ['task3', 'RES4', "2022-07-04 10:0$v:00", 'PT1M'], range(2, 6)),
                    ['task3', 'RES4', "2022-07-04 10:08:00", 'PT1M'],
                    ['task4', 'RES', "2022-07-04 10:00:00", 'PT10M'],
                ],
                ['task3 RES4 2022-07-04 10:00:00 PT2M', 'task3 RES4 2022-07-04 10:07:00 PT1M', 'task3 RES4 2022-07-04 10:09:00 PT1M'],
            ],
        ];
    }
}
