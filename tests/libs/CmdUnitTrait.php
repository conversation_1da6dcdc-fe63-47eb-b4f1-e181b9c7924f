<?php

declare(strict_types=1);

namespace app\tests\libs;

use app\back\components\CliArguments;
use app\back\components\Console;
use app\back\components\helpers\Json;
use app\back\components\ResponseCsv;
use app\back\config\tasks\Res;
use app\back\modules\task\commands\RunCommand;
use Symfony\Component\Process\Process;

trait CmdUnitTrait
{
    use DebugUnitTrait;

    protected const int LAST_MINUTE_PERIOD = 1;
    protected const int SKIP_EXIT_CODE_CHECK = 2;

    protected function runTask(string $task, string $resource = Res::DEFAULT, int|string|TaskDebugFile ...$params): int
    {
        [$taskParams, $skipExitCodeCheck] = self::buildTaskParams(...$params);
        $commandStr = "task/run $task $resource " . implode(' ', $taskParams);
        if (self::isDebugEnabled()) {
            self::debug($commandStr, $task);
        }

        $cliArguments = new CliArguments([$task, $resource, ...$taskParams], RunCommand::class, 'actionIndex');
        return $this->container()->withDefinitions([CliArguments::class => $cliArguments], function () use ($task, $commandStr, $cliArguments, $skipExitCodeCheck) {
            $command = $this->container()->create(RunCommand::class, $cliArguments->getAllParams());
            $exitCode = $this->wrapCliDebugOutput(fn() => $this->container()->call([$command, 'actionIndex'], $cliArguments->getAllParams()), $task);
            if (!self::isDebugEnabled()) {
                [$totalRows, $affectedRows] = $command->getTaskTotalAndAffected();
                self::debug("$commandStr => affected: $affectedRows/$totalRows exit_code: $exitCode");
            }
            if (!$skipExitCodeCheck) {
                self::assertSame(Console::EXIT_CODE_OK, $exitCode, "Task exit code $exitCode");
            }
            return $exitCode;
        });
    }

    protected function runCommandExpectedError(string ...$command): string
    {
        return $this->runCommandWithExitCode(Console::EXIT_CODE_UNSPECIFIED_ERROR, ...$command);
    }

    protected function runCommand(string ...$command): string
    {
        return $this->runCommandWithExitCode(Console::EXIT_CODE_OK, ...$command);
    }

    private function runCommandWithExitCode(int $exitCode, string ...$command): string
    {
        $prefix = 'CMD';

        $process = $this->createCommandProcess($command);
        self::debug("start {$process->getCommandLine()}", $prefix);

        $output = '';
        $process->start(static function (string $type, string $text) use (&$output, $prefix) {
            $output .= $text;
            self::debug(rtrim($text, "\n"), $prefix);
        });
        $process->wait();
        self::debug("exit {$process->getExitCode()} ({$process->getExitCodeText()})", $prefix);

        self::assertSame($exitCode, $process->getExitCode(), "CMD {$process->getCommandLine()}:\n" . trim($output) . "\nInvalid exit code {$process->getExitCode()} => {$process->getExitCodeText()}");
        return $output;
    }

    protected function createCommandProcess(array $command): Process
    {
        return new Process([BIN_ROOT . 'cli', ...$command]);
    }

    protected function haveTemporaryFile(string $content): string
    {
        $filename = uniqid('/tmp/analytics-test-', false) . '.txt';
        file_put_contents($filename, $content);
        chmod($filename, 0666);

        register_shutdown_function(static fn() => unlink($filename));

        return $filename;
    }

    protected function debugFile(string $content): TaskDebugFile
    {
        return new TaskDebugFile($this->haveTemporaryFile($content));
    }

    protected function csv(array $rows, string $separator = ','): TaskDebugFile
    {
        return $this->debugFile((new ResponseCsv($rows, null, $separator))->getContent());
    }

    protected function json(array $rows): TaskDebugFile
    {
        return $this->debugFile(Json::encode($rows));
    }

    private static function buildTaskParams(int|string|TaskDebugFile ...$params): array
    {
        $taskParams = [];
        $skipExitCodeCheck = false;
        foreach ($params as $param) {
            if ($param === self::LAST_MINUTE_PERIOD) {
                $taskParams[] = '-70 seconds';
                $taskParams[] = 'PT80S';
            } elseif ($param === self::SKIP_EXIT_CODE_CHECK) {
                $skipExitCodeCheck = true;
            } elseif ($param instanceof TaskDebugFile) {
                $taskParams[] = "--debugFile=$param->filename";
            } elseif (str_starts_with($param, "--debugFile=")) {
                self::fail('Use --debugFile via special methods $this->debugFileCsv(), $this->debugFileJson()...');
            } elseif ($param === '--debug') {
                self::fail('Task param "--debug" is dependent from phpunit --debug');
            } else {
                $taskParams[] = $param;
            }
        }

        if (self::isDebugEnabled()) {
            $taskParams[] = '--debug';
        }

        return [$taskParams, $skipExitCodeCheck];
    }
}
