<?php

declare(strict_types=1);

namespace app\tests\functional\api\crm;

use app\back\entities\Useragent;
use app\back\modules\api\clients\crm\info\InfoLoginPlatformsMethod;
use app\back\repositories\Useragents;
use app\back\repositories\UserLogins;
use app\tests\libs\ApiUnitTrait;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\TestCase;

#[CoversClass(InfoLoginPlatformsMethod::class)]
class CrmInfoLoginPlatformsTest extends TestCase
{
    use ApiUnitTrait;
    use DbTransactionalUnitTrait;

    public function testLoginPlatform(): void
    {
        $useragentRepo = $this->repo(Useragents::class);

        $useragentAndroidId = $useragentRepo->createDictionary()->getIdByName('Mozilla/5.0 (Linux; Android 15; 2406APNFAG Build/AP3A.240905.015.A2; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/137.0.7151.89 Mobile Safari/537.36MobileAppClient/Android/com.productwv.index76187.app/v100.21100.21/intbrowsrm');
        /** @var Useragent $useragentAndroidEntity */
        $useragentAndroidEntity = $useragentRepo->findOne(['id' => $useragentAndroidId]);

        $useragentIosId = $useragentRepo->createDictionary()->getIdByName('Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1');
        /** @var Useragent $useragentIosEntity */
        $useragentIosEntity = $useragentRepo->findOne(['id' => $useragentIosId]);

        $siteIdUserId = ['site_id' => self::uniqSiteId(), 'user_id' => self::uniqRuntimeId()];

        $this->haveUserLogins($siteIdUserId, 5, $useragentAndroidId);
        $this->haveUserLogins($siteIdUserId, 2, $useragentIosId);

        $this->sendAPI('crm', 'get/info-login-platforms', [
            'site_id' => ['=' => $siteIdUserId['site_id']],
            'user_id' => ['=' => $siteIdUserId['user_id']],
            'period_days' => ['=' => 30],
        ]);

        $this->seeRowsInCsv([
            [
                ...$siteIdUserId,
                'platform_id' => $useragentAndroidEntity->platform_id,
            ],
        ]);

        $this->haveUserLogins($siteIdUserId, 4, $useragentIosId);

        $this->sendAPI('crm', 'get/info-login-platforms', [
            'site_id' => ['=' => $siteIdUserId['site_id']],
            'user_id' => ['=' => $siteIdUserId['user_id']],
            'period_days' => ['=' => 30],
        ]);

        $this->seeRowsInCsv([
            [
                ...$siteIdUserId,
                'platform_id' => $useragentIosEntity->platform_id,
            ],
        ]);
    }

    private function haveUserLogins(array $siteIdUserId, int $countLogins, int $useragentId): void
    {
        for ($i = 1; $i <= $countLogins; $i++) {
            $this->haveRecord(UserLogins::class, [
                ...$siteIdUserId,
                'login_id' => uniqid('', true),
                'date' => new \DateTimeImmutable("-$i days"),
                'useragent_id' => $useragentId,
            ]);
        }
    }
}
