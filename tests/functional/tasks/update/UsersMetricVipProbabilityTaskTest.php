<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\update;

use app\back\config\tasks\Res;
use app\back\entities\Site;
use app\back\entities\Task;
use app\back\entities\User;
use app\back\entities\UserGameToken;
use app\back\entities\UserTransaction;
use app\back\modules\task\actions\update\UsersMetricVipProbabilityTask;
use app\back\repositories\TaskQueues;
use app\back\repositories\Tasks;
use app\back\repositories\UserSpecialInfos;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\ApiUnitTrait;
use app\tests\libs\DbCleanUnitTrait;
use app\tests\libs\mock\JsonBodyStream;
use app\tests\libs\mock\MockServer;
use Nyholm\Psr7\Request;
use Nyholm\Psr7\Response;
use PHPUnit\Framework\Attributes\CoversClass;
use Yiisoft\Db\Query\Query;

#[CoversClass(UsersMetricVipProbabilityTask::class)]
class UsersMetricVipProbabilityTaskTest extends BaseActionTestCase
{
    use ApiUnitTrait;
    use DbCleanUnitTrait;

    private const string QUEUE = 'TEST_QUEUE';

    public function testExpiredTime(): void
    {
        $siteId = Site::CV;
        $user = $this->haveUserRecord(['site_id' => $siteId, 'country' => 'TR', 'registration_method' => 1]);

        $this->haveRecord(UserSpecialInfos::class, [
            'site_id' => $siteId,
            'user_id' => $user->user_id,
            'dep_first_at' => new \DateTimeImmutable('-2 day')
        ]);
        $this->haveRates();
        $trans = $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' => $user->user_id,
            'op_id' => UserTransaction::OP_IN,
            'status' => UserTransaction::STATUS_SUCCESS,
            'wallet' => '12345678****9076',
            'is_first_success' => true,
            'amount_eur' => '100'
        ]);
        $gameToken = $this->haveUserGameTokenRecord([
            'site_id' => $siteId,
            'user_id' => $user->user_id,
            'balance_type' => UserGameToken::BALANCE_TYPE_REAL,
            'bet_amount_eur' => '75',
            'bet_count' => '1',
        ]);

        $this->checkCountInQueue(0);

        $from = new \DateTimeImmutable('-30 minutes');
        $this->addToQueue($from);

        MockServer::with(function () {
            $this->runCommand('task/start-peon', self::QUEUE, '--debug', '--exitOnNoPlanned');
        }, [
            [$this->mlHubRequest($user, $trans, $gameToken), $this->mlHubResponse()]
        ]);
        $this->checkCountInQueue(0);

        $from = new \DateTimeImmutable('-5 days');
        $this->addToQueue($from);

        $this->runCommandExpectedError('task/start-peon', self::QUEUE, '--debug', '--exitOnNoPlanned');

        $this->checkCountInQueue(1);
    }

    private function addToQueue(\DateTimeImmutable $from): void
    {
        /** @var Tasks $tasksRepo */
        $tasksRepo = $this->repo(Tasks::class);
        /** @var TaskQueues $taskQueuesRepo */
        $taskQueuesRepo = $this->repo(TaskQueues::class);

        $period = new \DateInterval('PT1H');

        $taskEntity = $tasksRepo->create(Task::SOURCE_CLI, 'update-users-metric-vip-probability', Res::CV, $from, $period);
        $taskQueuesRepo->create($taskEntity->id, self::QUEUE);

        $tasksCountInQueue = (new Query($this->db()))
            ->from(TaskQueues::TABLE_NAME)
            ->count();

        $this->assertSame(1, $tasksCountInQueue);
    }

    private function checkCountInQueue(int $count): void
    {
        $tasksCountInQueue = (new Query($this->db()))
            ->from(TaskQueues::TABLE_NAME)
            ->count();
        $this->assertSame($count, $tasksCountInQueue);
    }

    protected function mlHubRequest(User $user, UserTransaction $trans, UserGameToken $gameToken): Request
    {
        return new Request(
            'POST',
            "/api/model/infer/vip_classification",
            [],
            new JsonBodyStream(['in' => [[
                "site_id" => $user->site_id,
                "user_id" => $user->user_id,
                "most_frequent_status_card" => 'None',
                "registration_method" => $user->registration_method,
                "country" => $user->country,
                "dep_amount_eur" => $trans->amount_eur,
                "dep_count" => 1,
                "first_dep_sum_eur" => $trans->amount_eur,
                "bet_amount_eur" => $gameToken->bet_amount_eur,
                "bet_count" => $gameToken->bet_count,
                "bet_sum_per_spin_eur" => $gameToken->bet_amount_eur,
                "all_sessions_duration_sum" => "0",
                "avg_hours_between_deps" => "0",
                "win_amount_eur" => "0",
                "win_count" => 0,
                "games_sessions_count" => 1,
                "avg_dep_summ_eur" => $trans->amount_eur,
            ]]])
        );
    }

    protected function mlHubResponse(): Response
    {
        return new Response(
            200,
            [],
            new JsonBodyStream([
                "message" => 'Inference completed in 0.015s',
                "duration" => 0.015,
                "out" => [[
                    "vip_status_probability" => 0.0532,
                ]]
            ])
        );
    }
}
